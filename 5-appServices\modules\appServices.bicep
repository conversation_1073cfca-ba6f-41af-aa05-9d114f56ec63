metadata name = 'ALZ Bicep - PCP Application Infrastructure - App Services'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure App Services'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType

@sys.description('Main parameters')
param parLocation string
param parLocAbbrev string
param parEnvironment string
param parCompanyPrefix string
param parHubSubId string
param parSpokeSubId string
param parHubLogAnalyticsName string
param parHubNetworkResourceGroupName string
param parHubManagementResourceGroupName string
param parPrivateDNSZoneNameAppService string = 'privatelink.azurewebsites.net'
param parAppName string
param parSpokeName string
param parAppTags object = {}

@sys.description('Template specific parameters')
param parAppType string
param parServerFarmWebId string
param parServerFarmApiId string
param parAppHostName string
param parCustomerDomain string
param parAppDeployHostName bool
param parSubnetIDWebSrvInt string
param parSubnetIDApiSrvInt string
param parSCMIpSecurityRestrictions array = []
param parAppSettingsPrd array = []
param parAppSettingsAcc array = []
param parAppPrivateIpPrd string
param parAppPrivateIpAcc string
param parWafManagedRuleSetsPrd array = []
param parWafManagedRuleSetsAcc array = []
param parWafCustomRulesPrd array = []
param parWafCustomRulesAcc array = []
@sys.description('Path for the health check endpoint. If set, enables health check monitoring.')
param parHealthCheckPath string

@sys.description('Names of the Azure Front Door configurations')
param parFrontDoorEndpointName string
param parFrontDoorProfileName string

@sys.description('Array of alerts to create for the app service.')
param parAlerts array = []

@sys.description('Enable availability test for production environment.')
param parEnableAvailabilityTestPrd bool

@sys.description('Enable availability test for acceptance environment.')
param parEnableAvailabilityTestAcc bool

@sys.description('Enable health check alerting for production environment. Only used when healthCheckPath is set.')
param parEnableHealthCheckAlertingPrd bool

@sys.description('Enable health check alerting for acceptance environment. Only used when healthCheckPath is set.')
param parEnableHealthCheckAlertingAcc bool

param parApplicationInsightsLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure App Services.'
}
param parAppServiceLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure App Services.'
}
param parAppServicePrivateEndpointLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure App Services.'
}
param parAppServiceSlotLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure App Services.'
}
param parAppServiceSlotPrivateEndpointLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure App Services.'
}

param parActionGroupDevId string
param parActionGroupOpsId string
param parActionGroupTypeAcc string
#disable-next-line no-unused-params
param parActionGroupTypePrd string

@sys.description('Normalized environment: maps prd/prd2 => prd; otherwise lower-cased parEnvironment')
var varEnvironment = contains(['prd', 'prd2'], toLower(parEnvironment)) ? 'prd' : toLower(parEnvironment)

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varApiInboundSubnetId = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'apiAppsInboundSubnet')
var varWebInboundSubnetId = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'webAppsInboundSubnet')
var varVirtualNetworkIdToLink = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks', 'vnet-${parSpokeName}-${varLocAbbrev}')

var varPrivateDNSZoneAppService = resourceId(parHubSubId, parHubNetworkResourceGroupName, 'Microsoft.Network/privateDnsZones', 'privatelink.azurewebsites.net')

@sys.description('Call the existing Log Analytics workspace')
resource resLogAnalyticsWorkspace 'Microsoft.OperationalInsights/workspaces@2025-02-01' existing = {
  name: parHubLogAnalyticsName
  scope: resourceGroup(parHubSubId, parHubManagementResourceGroupName)
}

@sys.description('Call existing APP key vault resources')
resource resAppKeyVault 'Microsoft.KeyVault/vaults@2024-11-01' existing = {
  name: 'kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev}'
}
@sys.description('Set variables for the Key Vault Secrets Officer role definition')
var varKeyVaultSecretsOfficerRoleDefinitionId = resourceId(parSpokeSubId, 'Microsoft.Authorization/roleDefinitions', 'b86a8fe4-44ce-4948-aee5-eccb2c155cd7')

@sys.description('Set variables for the Blob Role definition')
var varblobContributorRoleDefinitionId = resourceId(parSpokeSubId, 'Microsoft.Authorization/roleDefinitions', 'ba92f5b4-2d11-453d-a403-e96b0029c9fe')

@sys.description('Set health check path to null if not set, so it is not used in the app service configuration')
var varHealthCheckPath = empty(parHealthCheckPath) ? null : parHealthCheckPath

@sys.description('Name of the slot based on environment (dev or acc)')
var varSlotName = parEnvironment == 'dev' ? 'dev' : 'acc'

var varAlerts = concat(
  parAlerts,
  parEnableAvailabilityTestPrd
    ? [
        {
          name: parAppType
          condition: 'available'
          description: 'Alert when ${parAppType} app is not available'
          severity: 1
          alertType: 'WebTest'
          webTestId: modWebTestPrd.outputs.webTestId
          targetResourceId: resApplicationInsights.id
          actionGroupType: parActionGroupTypeAcc
        }
      ]
    : [],
  parEnableAvailabilityTestAcc
    ? [
        {
          name: '${parAppType}-${varSlotName}'
          condition: 'available-${varSlotName}'
          description: 'Alert when ${parAppType}-${varSlotName} app is not available'
          severity: 1
          alertType: 'WebTest'
          webTestId: modWebTestAcc.outputs.webTestId
          targetResourceId: resApplicationInsights.id
          actionGroupType: parActionGroupTypeAcc
        }
      ]
    : [],
  !empty(parHealthCheckPath) && parEnableHealthCheckAlertingPrd
    ? [
        {
          name: parAppType
          condition: 'healthcheck'
          description: 'Alert when ${parAppType} healthcheck status is below threshold'
          severity: 1
          alertType: 'HealthCheck'
          targetResourceId: resAppService.id
          actionGroupType: parActionGroupTypeAcc
        }
      ]
    : [],
  !empty(parHealthCheckPath) && parEnableHealthCheckAlertingAcc
    ? [
        {
          name: '${parAppType}-${varSlotName}'
          condition: 'healthcheck-${varSlotName}'
          description: 'Alert when ${parAppType}-${varSlotName} healthcheck status is below threshold'
          severity: 1
          alertType: 'HealthCheck'
          targetResourceId: '${resAppService.id}/slots/${varSlotName}'
          actionGroupType: parActionGroupTypeAcc
        }
      ]
    : []
)

@sys.description('Based on the environments, get the storage account names to set as app settings for the specific app service slots')
resource resAppStorageAccountPrd 'Microsoft.Storage/storageAccounts@2025-01-01' existing = if (varEnvironment == 'prd' && parAppType == 'api') {
  name: toLower('saprd${parAppName}${(parEnvironment == 'prd2' ? 'prd2' : '')}${parLocAbbrev}')
}
resource resAppStorageAccountAcc 'Microsoft.Storage/storageAccounts@2025-01-01' existing = if (varEnvironment == 'prd' && parAppType == 'api') {
  name: toLower('saacc${parAppName}${(parEnvironment == 'prd2' ? 'prd2' : '')}${parLocAbbrev}')
}
resource resAppStorageAccountTst 'Microsoft.Storage/storageAccounts@2025-01-01' existing = if (varEnvironment == 'dev' && parAppType == 'api') {
  name: toLower('satst${parAppName}${(parEnvironment == 'prd2' ? 'prd2' : '')}${parLocAbbrev}')
}
resource resAppStorageAccountDev 'Microsoft.Storage/storageAccounts@2025-01-01' existing = if (varEnvironment == 'dev' && parAppType == 'api') {
  name: toLower('sadev${parAppName}${(parEnvironment == 'prd2' ? 'prd2' : '')}${parLocAbbrev}')
}

@sys.description('Create the Application Insights resource')
resource resApplicationInsights 'Microsoft.Insights/components@2020-02-02' = {
  name: 'appi-${parAppName}-${parAppType}-${parEnvironment}-${parLocAbbrev}'
  location: parLocation
  kind: 'web'
  properties: {
    Application_Type: 'web'
    RetentionInDays: 90
    WorkspaceResourceId: resLogAnalyticsWorkspace.id
  }
}

@sys.description('Create web tests and alerts for app insights')
module modWebTestPrd './webTest.bicep' = if (parEnableAvailabilityTestPrd) {
  name: 'webtest-${parAppType}-available'
  params: {
    parName: 'webtest-${parSpokeName}-${parAppType}-available'
    parAppInsightsName: resApplicationInsights.name
    parLocation: parLocation
    parRequestUrl: 'https://${parAppHostName}.${parCustomerDomain}/'
    parDisplayName: '${parAppType} available'
  }
}

module modWebTestAcc './webTest.bicep' = if (parEnableAvailabilityTestAcc) {
  name: 'webtest-${parAppType}-available-${varSlotName}'
  params: {
    parName: 'webtest-${parSpokeName}-${parAppType}-available-${varSlotName}'
    parAppInsightsName: resApplicationInsights.name
    parLocation: parLocation
    parRequestUrl: 'https://${parAppHostName}-${varSlotName}.${parCustomerDomain}/'
    parDisplayName: '${parAppType}-${varSlotName} available'
  }
}

module modAlerts './appServicesAlerts.bicep' = {
  name: 'alerts-${parAppName}-${parAppType}'
  params: {
    parLocAbbrev: parLocAbbrev
    parAppName: parAppName
    parEnvironment: parEnvironment
    parAppType: parAppType
    parLocation: parLocation
    parAlerts: varAlerts
    parActionGroupDevId: parActionGroupDevId
    parActionGroupOpsId: parActionGroupOpsId
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resApplicationInsightsLock 'Microsoft.Authorization/locks@2020-05-01' = if (parApplicationInsightsLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resApplicationInsights
  name: parApplicationInsightsLock.?name ?? '${resApplicationInsights.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parApplicationInsightsLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parApplicationInsightsLock.?notes
  }
}

@sys.description('Create the PRD App Service')
resource resAppService 'Microsoft.Web/sites@2024-11-01' = {
  name: 'app-${parAppName}-${parAppType}-${parEnvironment}-${parLocAbbrev}'
  location: parLocation
  kind: 'app'
  tags: union(parAppTags, {
    workloadtype: 'appservice'
    deploymentslot: parEnvironment
  })
  identity: {
    type: 'SystemAssigned'
  }
  properties: {
    serverFarmId: contains(parAppType, 'api') ? parServerFarmApiId : parServerFarmWebId
    virtualNetworkSubnetId: contains(parAppType, 'api') ? parSubnetIDApiSrvInt : parSubnetIDWebSrvInt
    httpsOnly: true
    clientAffinityEnabled: true
    clientCertEnabled: false
    endToEndEncryptionEnabled: true
    siteConfig: {
      linuxFxVersion: contains(parAppType, 'api') ? 'DOTNETCORE|8.0' : 'NODE|20-lts'
      appCommandLine: contains(parAppType, 'api') ? 'dotnet NetProGroup.Trust.API.dll' : 'node ./node_modules/.bin/remix-serve ./build/server/index.js'
      vnetRouteAllEnabled: true
      http20Enabled: true
      alwaysOn: true
      ftpsState: 'Disabled'
      minTlsVersion: '1.3'
      use32BitWorkerProcess: true
      webSocketsEnabled: false
      publicNetworkAccess: 'Disabled'
      scmIpSecurityRestrictions: parSCMIpSecurityRestrictions
      scmIpSecurityRestrictionsDefaultAction: 'Deny'
      scmMinTlsVersion: '1.3'
      healthCheckPath: varHealthCheckPath
      appSettings: union(parAppSettingsPrd, [
        {
          name: 'APPINSIGHTS_CONNECTIONSTRING'
          value: resApplicationInsights.properties.ConnectionString
        }
        {
          name: 'APPINSIGHTS_INSTRUMENTATIONKEY'
          value: resApplicationInsights.properties.InstrumentationKey
        }
        {
          name: 'XDT_MicrosoftApplicationInsights_Mode'
          value: 'recommended'
        }
        {
          name: 'ApplicationInsightsAgent_EXTENSION_VERSION'
          value: '~2'
        }
        {
          name: 'BlobStorage__AccountName'
          value: varEnvironment == 'prd' ? resAppStorageAccountPrd.name : resAppStorageAccountTst.name
        }
      ])
    }
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAppServiceLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAppServiceLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resAppService
  name: parAppServiceLock.?name ?? '${resAppService.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAppServiceLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAppServiceLock.?notes
  }
}

@sys.description('Create a Connection string for the App Service')
resource resConnectionStringsPrd 'Microsoft.Web/sites/config@2024-11-01' = if (contains(parAppType, 'api')) {
  name: 'connectionstrings'
  parent: resAppService
  properties: {
    default: {
      value: 'server=tcp:sqlsrv-${parAppName}-${parEnvironment}-${parLocAbbrev}.database.windows.net;database=sqldb-${parAppName}-${varEnvironment == 'prd' ? 'prd' : 'tst'};Authentication=Active Directory Default'
      type: 'SQLServer'
    }
  }
}

@sys.description('Slot app settings configuration Slot specific (deployment slot)')
resource resSlotConfigPrd 'Microsoft.Web/sites/config@2024-11-01' = {
  name: 'slotConfigNames'
  parent: resAppService
  properties: {
    appSettingNames: [
      'BlobStorage__AccountName'
      'ASPNETCORE_ENVIRONMENT'
      'APPLICATION_BASE_URL'
      'APPLICATION_CLIENT_ID'
      'APPLICATION_CLIENT_SECRET'
      'ENTRA_EXTERNAL_ID_CLIENT_ID'
      'ENTRA_EXTERNAL_ID_CLIENT_SECRET'
      'ENTRA_EXTERNAL_ID_REDIRECT_URI'
      'API_BASE_URL'
      'APPLICATION_LOGO_URL'
      'ENTRA_API_SCOPE'
      'ENTRA_CLIENT_ID'
      'ENTRA_CLIENT_SECRET'
      'ENTRA_POST_LOGOUT_REDIRECT_URI'
      'ENTRA_REDIRECT_URI'
      'AppRegistration__ClientId'
      'AppRegistration__ClientSecret'
      'AzureAd__ClientId'
      'AzureAd__Audience'
      'ExternalId__ClientId'
      'DataSeedEnvironment'
      'DataSync__Enabled'
      'MongoDb__DatabaseName'
      'ConnectionStrings__MongoDb'
      'SMTP__Password'
    ]
    connectionStringNames: [
      'default'
    ]
  }
}

@sys.description('Assign the App Service PrincipalId to the Key Vault as Secret Officer')
resource resRoleAssignmentKeyvaultPrd 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(resAppKeyVault.id, resAppService.id, varKeyVaultSecretsOfficerRoleDefinitionId)
  scope: resAppKeyVault
  properties: {
    roleDefinitionId: varKeyVaultSecretsOfficerRoleDefinitionId
    principalId: resAppService.identity.principalId
    principalType: 'ServicePrincipal'
  }
}

@sys.description('Create the diagnostic settings for the App Service')
resource resAppServiceDiags 'Microsoft.Insights/diagnosticSettings@2021-05-01-preview' = {
  name: 'diag-${resAppService.name}'
  scope: resAppService
  properties: {
    logs: [
      {
        category: 'AppServiceAntivirusScanAuditLogs'
        enabled: true
      }
      {
        category: 'AppServiceHTTPLogs'
        enabled: true
      }
      {
        category: 'AppServiceConsoleLogs'
        enabled: true
      }
      {
        category: 'AppServiceAppLogs'
        enabled: true
      }
      {
        category: 'AppServiceFileAuditLogs'
        enabled: true
      }
      {
        category: 'AppServiceAuditLogs'
        enabled: true
      }
      {
        category: 'AppServiceIPSecAuditLogs'
        enabled: true
      }
      {
        category: 'AppServicePlatformLogs'
        enabled: true
      }
    ]
    metrics: [
      {
        category: 'AllMetrics'
        enabled: true
      }
    ]
    workspaceId: resLogAnalyticsWorkspace.id
    logAnalyticsDestinationType: null
  }
}

@sys.description('Create the logs for the App Service')
resource resAppServiceLogs 'Microsoft.Web/sites/config@2024-11-01' = {
  name: 'logs'
  parent: resAppService
  properties: {
    detailedErrorMessages: {
      enabled: true
    }
    failedRequestsTracing: {
      enabled: true
    }
    httpLogs: {
      fileSystem: {
        enabled: true
        retentionInMb: 35
        retentionInDays: 30
      }
    }
  }
}

@sys.description('Create the private endpoint for the API App Service, the Frontend apps do not need this as they will get a private endpoint in the Azure Front Door')
resource resAppServicePrivateEndpoint 'Microsoft.Network/privateEndpoints@2024-07-01' = {
  name: 'pep-${resAppService.name}'
  location: parLocation
  tags: union(parAppTags, {
    workloadtype: 'networking'
    environment: parEnvironment
  })
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'sites'
          memberName: 'sites'
          privateIPAddress: parAppPrivateIpPrd
        }
      }
    ]
    subnet: {
      id: parAppType == 'api' ? varApiInboundSubnetId : varWebInboundSubnetId
    }
    privateLinkServiceConnections: [
      {
        name: 'pep-${resAppService.name}'
        properties: {
          privateLinkServiceId: resAppService.id
          groupIds: [
            'sites'
          ]
        }
      }
    ]
  }
}

@sys.description('Create a resource lock for the Private endpoint if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAppServicePrivateEndpointLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAppType == 'api' && ((parAppServicePrivateEndpointLock.kind != 'None' || parGlobalResourceLock.kind != 'None'))) {
  scope: resAppServicePrivateEndpoint
  name: parAppServicePrivateEndpointLock.?name ?? '${resAppServicePrivateEndpoint.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAppServicePrivateEndpointLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAppServicePrivateEndpointLock.?notes
  }
}

@sys.description('Create the private DNS Zone Group for the App Service private endpoint')
resource resAppServiceprivateDNSZoneGroup 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2024-07-01' = {
  parent: resAppServicePrivateEndpoint
  name: 'dnsgroupname-${resAppService.name}'
  properties: {
    privateDnsZoneConfigs: [
      {
        name: 'config1'
        properties: {
          privateDnsZoneId: varPrivateDNSZoneAppService
        }
      }
    ]
  }
}

@sys.description('Create acceptance slot for the app service')
resource resAppServiceSlot 'Microsoft.Web/sites/slots@2024-11-01' = {
  name: varSlotName
  parent: resAppService
  location: parLocation
  kind: 'app'
  tags: union(parAppTags, {
    workloadtype: 'appservice'
    deploymentslot: varSlotName
  })
  identity: {
    type: 'SystemAssigned'
  }
  properties: {
    serverFarmId: contains(parAppType, 'api') ? parServerFarmApiId : parServerFarmWebId
    virtualNetworkSubnetId: contains(parAppType, 'api') ? parSubnetIDApiSrvInt : parSubnetIDWebSrvInt
    httpsOnly: true
    clientAffinityEnabled: true
    clientCertEnabled: false
    endToEndEncryptionEnabled: true
    siteConfig: {
      linuxFxVersion: contains(parAppType, 'api') ? 'DOTNETCORE|8.0' : 'NODE|20-lts'
      appCommandLine: contains(parAppType, 'api') ? 'dotnet NetProGroup.Trust.API.dll' : 'node ./node_modules/.bin/remix-serve ./build/server/index.js'
      vnetRouteAllEnabled: true
      http20Enabled: true
      alwaysOn: true
      ftpsState: 'Disabled'
      minTlsVersion:'1.3'
      use32BitWorkerProcess: true
      webSocketsEnabled: false
      publicNetworkAccess: 'Disabled'
      scmIpSecurityRestrictions: parSCMIpSecurityRestrictions
      scmIpSecurityRestrictionsDefaultAction: 'Deny'
      scmMinTlsVersion:'1.3'
      healthCheckPath: varHealthCheckPath
      appSettings: union(parAppSettingsAcc, [{
          name: 'APPINSIGHTS_CONNECTIONSTRING'
          value: resApplicationInsights.properties.ConnectionString
        }
        {
          name: 'APPINSIGHTS_INSTRUMENTATIONKEY'
          value: resApplicationInsights.properties.InstrumentationKey
        }
        {
          name: 'XDT_MicrosoftApplicationInsights_Mode'
          value: 'recommended'
        }
        {
          name: 'ApplicationInsightsAgent_EXTENSION_VERSION'
          value: '~2'
        }
        {
          name: 'BlobStorage__AccountName'
          value: varEnvironment == 'prd' ? resAppStorageAccountAcc.name : resAppStorageAccountDev.name
        }
      ])
    }
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAppServiceSlotLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAppServiceSlotLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resAppServiceSlot
  name: parAppServiceSlotLock.?name ?? '${resAppServiceSlot.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAppServiceSlotLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAppServiceSlotLock.?notes
  }
}

@sys.description('Connection string for the app service slot')
resource resConnectionStringsSlot 'Microsoft.Web/sites/slots/config@2024-11-01' = if (contains(parAppType, 'api')) {
  name: 'connectionstrings'
  parent: resAppServiceSlot
  properties: {
    default: {
      value: 'server=tcp:sqlsrv-${parAppName}-${parEnvironment}-${parLocAbbrev}.database.windows.net;database=sqldb-${parAppName}-${varSlotName};Authentication=Active Directory Default'
      type: 'SQLServer'
    }
  }
}

@sys.description('role assignment for the app service slot on the key vault secret officer')
resource resRoleAssignmentKeyvaultAcc 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(resAppKeyVault.id, resAppServiceSlot.id, varKeyVaultSecretsOfficerRoleDefinitionId)
  scope: resAppKeyVault
  properties: {
    roleDefinitionId: varKeyVaultSecretsOfficerRoleDefinitionId
    principalId: resAppServiceSlot.identity.principalId
    principalType: 'ServicePrincipal'
  }
}

@sys.description('Create the diagnostic settings for the App Service Slot')
resource resAppServiceSlotDiags 'Microsoft.Insights/diagnosticSettings@2021-05-01-preview' = {
  name: 'diag-${resAppServiceSlot.name}'
  scope: resAppServiceSlot
  properties: {
    logs: [
      {
        category: 'AppServiceAntivirusScanAuditLogs'
        enabled: true
      }
      {
        category: 'AppServiceHTTPLogs'
        enabled: true
      }
      {
        category: 'AppServiceConsoleLogs'
        enabled: true
      }
      {
        category: 'AppServiceAppLogs'
        enabled: true
      }
      {
        category: 'AppServiceFileAuditLogs'
        enabled: true
      }
      {
        category: 'AppServiceAuditLogs'
        enabled: true
      }
      {
        category: 'AppServiceIPSecAuditLogs'
        enabled: true
      }
      {
        category: 'AppServicePlatformLogs'
        enabled: true
      }
    ]
    metrics: [
      {
        category: 'AllMetrics'
        enabled: true
      }
    ]
    workspaceId: resLogAnalyticsWorkspace.id
    logAnalyticsDestinationType: null
  }
}

@sys.description('Create the logs for the App Service Slot')
resource resAppServiceSlotLogs 'Microsoft.Web/sites/slots/config@2024-11-01' = {
  name: 'logs'
  parent: resAppServiceSlot
  properties: {
    detailedErrorMessages: {
      enabled: true
    }
    failedRequestsTracing: {
      enabled: true
    }
    httpLogs: {
      fileSystem: {
        enabled: true
        retentionInMb: 35
        retentionInDays: 30
      }
    }
  }
}

@sys.description('Create the private endpoint for the API App Service Slot')
resource resAppServiceSlotPrivateEndpoint 'Microsoft.Network/privateEndpoints@2024-07-01' = {
  name: 'pep-${resAppService.name}-${resAppServiceSlot.name}'
  location: parLocation
  tags: union(parAppTags, {
    workloadtype: 'networking'
    environment: parEnvironment
  })
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'sites-${resAppServiceSlot.name}'
          memberName: 'sites-${resAppServiceSlot.name}'
          privateIPAddress: parAppPrivateIpAcc
        }
      }
    ]
    subnet: {
      id: parAppType == 'api' ? varApiInboundSubnetId : varWebInboundSubnetId
    }
    privateLinkServiceConnections: [
      {
        name: 'pep-${resAppService.name}-${resAppServiceSlot.name}'
        properties: {
          privateLinkServiceId: resAppService.id
          groupIds: [
            'sites-${resAppServiceSlot.name}'
          ]
        }
      }
    ]
  }
}

@sys.description('Create a resource lock for the Private endpoint if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAppServiceSlotPrivateEndpointLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAppType == 'api' && ((parAppServiceSlotPrivateEndpointLock.kind != 'None' || parGlobalResourceLock.kind != 'None'))) {
  scope: resAppServiceSlotPrivateEndpoint
  name: parAppServiceSlotPrivateEndpointLock.?name ?? '${resAppServiceSlotPrivateEndpoint.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAppServiceSlotPrivateEndpointLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAppServiceSlotPrivateEndpointLock.?notes
  }
}

@sys.description('Create the private DNS Zone Group for the App Service Slot private endpoint')
resource resAppServiceSlotprivateDNSZoneGroup 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2024-07-01' = {
  parent: resAppServiceSlotPrivateEndpoint
  name: 'dnsgroupname-${resAppService.name}'
  properties: {
    privateDnsZoneConfigs: [
      {
        name: 'config1'
        properties: {
          privateDnsZoneId: varPrivateDNSZoneAppService
        }
      }
    ]
  }
}

@sys.description('Call Module that creates the DNS Link between the Private DNS Zone and the vNet to which the app service is connected')
module modAppServiceDNSLink '../../subModules/privateDNSLink/privateDNSLink.bicep' = if (parAppType == 'api') {
  name: 'appServiceDNSLinkSlot-${guid(resAppServiceSlot.id, resAppServiceSlotPrivateEndpoint.id)}'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parSpoke: parSpokeName
    parPrivateDnsZoneName: parPrivateDNSZoneNameAppService
    parVirtualNetworkIdToLink: varVirtualNetworkIdToLink
  }
}

@description('Assign only APIs PrincipalId to Storage accounts')
resource resRoleAssignmentSaPrd 'Microsoft.Authorization/roleAssignments@2022-04-01' = if (varEnvironment == 'prd' && parAppType == 'api') {
  name: guid(resAppStorageAccountPrd.id, resAppService.id, varblobContributorRoleDefinitionId)
  scope: resAppStorageAccountPrd
  properties: {
    roleDefinitionId: varblobContributorRoleDefinitionId
    principalId: resAppService.identity.principalId
    principalType: 'ServicePrincipal'
  }
}
resource resRoleAssignmentSaAcc 'Microsoft.Authorization/roleAssignments@2022-04-01' = if (varEnvironment == 'prd' && parAppType == 'api') {
  name: guid(resAppStorageAccountAcc.id, resAppServiceSlot.id, varblobContributorRoleDefinitionId)
  scope: resAppStorageAccountAcc
  properties: {
    roleDefinitionId: varblobContributorRoleDefinitionId
    principalId: resAppServiceSlot.identity.principalId
    principalType: 'ServicePrincipal'
  }
}
resource resRoleAssignmentSaTst 'Microsoft.Authorization/roleAssignments@2022-04-01' = if (varEnvironment == 'dev' && parAppType == 'api') {
  name: guid(resAppStorageAccountTst.id, resAppService.id, varblobContributorRoleDefinitionId)
  scope: resAppStorageAccountTst
  properties: {
    roleDefinitionId: varblobContributorRoleDefinitionId
    principalId: resAppService.identity.principalId
    principalType: 'ServicePrincipal'
  }
}
resource resRoleAssignmentSaDev 'Microsoft.Authorization/roleAssignments@2022-04-01' = if (varEnvironment == 'dev' && parAppType == 'api') {
  name: guid(resAppStorageAccountDev.id, resAppServiceSlot.id, varblobContributorRoleDefinitionId)
  scope: resAppStorageAccountDev
  properties: {
    roleDefinitionId: varblobContributorRoleDefinitionId
    principalId: resAppServiceSlot.identity.principalId
    principalType: 'ServicePrincipal'
  }
}

@description('Call Module that creates the Azure Front Door Endpoint, Origin Group, Origin and Routing Rule')
module modFrontDoorOriginRoutePrd 'afdOriginRoute.bicep' = if (!contains(parAppType, 'api')) {
  name: '${parAppType}-AFDOriginRouteApp'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parEnvironment: parEnvironment
    parAppName: parAppName
    parFrontDoorProfileName: parFrontDoorProfileName
    parFrontDoorEndpointName: parFrontDoorEndpointName    
    parAppType: parAppType
    parSlotName: varEnvironment == 'prd' ? 'prd' : 'tst'
    parAppHostName: '${parAppHostName}.${parCustomerDomain}'
    parAppDeployHostName: parAppDeployHostName
    parAppDefaultHostName: resAppService.properties.defaultHostName
    parPrivateEndpointResourceId: resAppService.id
    parPrivateLinkResourceType: 'sites'
    parPrivateEndpointLocation: parLocation
    parWafManagedRuleSets: parWafManagedRuleSetsPrd
    parWafCustomRules: parWafCustomRulesPrd
  }
}

module modFrontDoorOriginRouteAcc 'afdOriginRoute.bicep' = if (!contains(parAppType, 'api')) {
  name: '${parAppType}-AFDOriginRouteSlot'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parEnvironment: parEnvironment
    parAppName: parAppName
    parFrontDoorProfileName: parFrontDoorProfileName
    parFrontDoorEndpointName: parFrontDoorEndpointName
    parAppType: parAppType
    parSlotName: varSlotName
    parAppHostName: '${parAppHostName}-${varSlotName}.${parCustomerDomain}'
    parAppDeployHostName: parAppDeployHostName
    parAppDefaultHostName: resAppServiceSlot.properties.defaultHostName
    parPrivateEndpointResourceId: resAppService.id
    parPrivateLinkResourceType: 'sites-${resAppServiceSlot.name}'
    parPrivateEndpointLocation: parLocation
    parWafManagedRuleSets: parWafManagedRuleSetsAcc
    parWafCustomRules: parWafCustomRulesAcc
  }
}
