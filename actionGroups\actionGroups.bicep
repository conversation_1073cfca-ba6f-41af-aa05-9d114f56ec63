@description('Logic App HTTP trigger callback URL (the long .../triggers/manual/... URL) to post alerts to the dev team Teams channel')
param parDevTeamChannelAlertLogicAppUrl string

@description('Email address to send alerts to NetPro Support')
param parNetProSupportEmail string

@description('Short name of the dev team action group (max 12 characters)')
param parDevTeamActionGroupShortName string

@description('Display name for the dev team action group')
param parDevTeamActionGroupDisplayName string

@description('Short name of the NetPro support action group (max 12 characters)')
param parNetProSupportActionGroupShortName string

@description('Display name for the NetPro support action group')
param parNetProSupportActionGroupDisplayName string

module modActionGroupDevTeam './modules/actionGroupDevTeam.bicep' = {
  name: 'actionGroupDevTeam'
  params: {
    parTeamsChannelLogicAppTriggerUrl: parDevTeamChannelAlertLogicAppUrl
    parActionGroupShortName: parDevTeamActionGroupShortName
    parActionGroupDisplayName: parDevTeamActionGroupDisplayName
  }
}

module modActionGroupOpsTeam './modules/actionGroupOpsTeam.bicep' = {
  name: 'actionGroupOpsTeam'
  params: {
    parNetProSupportEmail: parNetProSupportEmail
    parActionGroupShortName: parNetProSupportActionGroupShortName
    parActionGroupDisplayName: parNetProSupportActionGroupDisplayName
  }
}

output outOpsTeamActionGroupId string = modActionGroupOpsTeam.outputs.actionGroupId
output outDevTeamActionGroupId string = modActionGroupDevTeam.outputs.actionGroupId
