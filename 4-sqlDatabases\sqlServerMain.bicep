metadata name = 'ALZ Bicep - PCP Application Infrastructure - SQL Server/ Databases'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure SQL Server/ Databases'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType

param parLocation string
param parEnvironment string
param parLocAbbrev string
param parSpokeName string
param parAppName string
param parSecGroupNameSql string
param parSecGroupSidSql string
param parSqlSkuNamePrd string
param parSqlSkuFamilyPrd string
param parSqlSkuSizePrd string
param parSqlSkuTierPrd string
param parSqlSkuCapacityPrd int
param parSqlMaxSizeBytesPrd int
param parSqlSkuNameAcc string
param parSqlSkuFamilyAcc string
param parSqlSkuSizeAcc string
param parSqlSkuTierAcc string
param parSqlSkuCapacityAcc int
param parSqlMaxSizeBytesAcc int
param parSqlZoneRedundantPrd bool
param parSqlZoneRedundantAcc bool
param parHubSubId string
param parCompanyPrefix string
param parSpokeSubId string
param parSqlIpAddress string
param parHubLogAnalyticsName string
param parHubNetworkResourceGroupName string
// param parDeployScriptsIdentity string  // Temporarily disabled - only used by SQL permissions module
// param parDeploymentScriptSaName string  // Temporarily disabled - only used by SQL permissions module
// param parAppResourceGroupName string  // Temporarily disabled - only used by SQL permissions module
// param parHubIdResourceGroupName string  // Temporarily disabled - only used by SQL permissions module
param parHubManagementResourceGroupName string
// param parDirReaderGroupSid string  // Temporarily disabled - only used by SQL permissions module
param parSqlTags object = {}

module modSqlServerMain '../4-sqlDatabases/modules/sqlServer.bicep' = {
  name: 'sqlServerMain'
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parCompanyPrefix: parCompanyPrefix
    parEnvironment: parEnvironment
    parSqlTags: parSqlTags
    parSpokeName: parSpokeName
    parAppName: parAppName
    parSecGroupNameSql: parSecGroupNameSql
    parSecGroupSidSql: parSecGroupSidSql
    parSqlSkuNamePrd: parSqlSkuNamePrd
    parSqlSkuFamilyPrd: parSqlSkuFamilyPrd
    parSqlSkuSizePrd: parSqlSkuSizePrd
    parSqlSkuTierPrd: parSqlSkuTierPrd
    parSqlSkuCapacityPrd: parSqlSkuCapacityPrd
    parSqlMaxSizeBytesPrd: parSqlMaxSizeBytesPrd
    parSqlSkuNameAcc: parSqlSkuNameAcc
    parSqlSkuFamilyAcc: parSqlSkuFamilyAcc
    parSqlSkuSizeAcc: parSqlSkuSizeAcc
    parSqlSkuTierAcc: parSqlSkuTierAcc
    parSqlSkuCapacityAcc: parSqlSkuCapacityAcc
    parSqlMaxSizeBytesAcc: parSqlMaxSizeBytesAcc
    parSqlZoneRedundantPrd: parSqlZoneRedundantPrd
    parSqlZoneRedundantAcc: parSqlZoneRedundantAcc
    parSpokeSubId: parSpokeSubId
    parHubSubId: parHubSubId
    parSqlIpAddress: parSqlIpAddress
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parHubManagementResourceGroupName: parHubManagementResourceGroupName
    parHubLogAnalyticsName: parHubLogAnalyticsName
  }
}

// module modSqlServerPermissions '../4-sqlDatabases/modules/sqlServerPermissions.bicep' = {
//   name: 'sqlServerPermissions'
//   params: {
//     parLocation: parLocation
//     parLocAbbrev: parLocAbbrev
//     parCompanyPrefix: parCompanyPrefix
//     parSpokeName: parSpokeName
//     parDeployScriptsIdentity: parDeployScriptsIdentity
//     parDeploymentScriptSaName: parDeploymentScriptSaName
//     parSqlServerName: modSqlServerMain.outputs.outSqlServerName
//     parSpokeSubId: parSpokeSubId
//     parHubSubId: parHubSubId
//     parAppResourceGroupName: parAppResourceGroupName
//     parHubIdResourceGroupName: parHubIdResourceGroupName
//     parDirReaderGroupSid: parDirReaderGroupSid
//   }
// }
