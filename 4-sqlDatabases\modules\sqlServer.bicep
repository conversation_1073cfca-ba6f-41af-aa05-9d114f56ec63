metadata name = 'ALZ Bicep - PCP Application Infrastructure - SQL Databases'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure SQL Databases'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType

@description('Main parameters')
param parLocation string
param parLocAbbrev string
param parCompanyPrefix string
param parEnvironment string
param parSqlTags object = {}
param parSpokeName string
param parAppName string

@description('Template specific parameters')
param parSecGroupNameSql string
@secure()
param parSecGroupSidSql string
param parSqlSkuNamePrd string
param parSqlSkuFamilyPrd string
param parSqlSkuSizePrd string
param parSqlSkuTierPrd string
param parSqlSkuCapacityPrd int
param parSqlMaxSizeBytesPrd int
param parSqlSkuNameAcc string
param parSqlSkuFamilyAcc string
param parSqlSkuSizeAcc string
param parSqlSkuTierAcc string
param parSqlSkuCapacityAcc int
param parSqlMaxSizeBytesAcc int
param parSqlZoneRedundantPrd bool
param parSqlZoneRedundantAcc bool
param parSpokeSubId string
param parHubSubId string
param parSqlIpAddress string
param parHubNetworkResourceGroupName string
param parHubManagementResourceGroupName string
param parHubLogAnalyticsName string
#disable-next-line no-hardcoded-env-urls
param parPrivateDnsZoneSqlName string = 'privatelink.database.windows.net'

param parSqlServerLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure SQL Server/ Databases.'
}
param parSqlDBprdLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure SQL Server/ Databases.'
}
param parSqlDBaccLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure SQL Server/ Databases.'
}
param parSqlSrvPrivateEndpoint01Lock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure SQL Server/ Databases.'
}

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varCustomAppsSubnetId = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'databaseSubnet')
var varVirtualNetworkIdToLink = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks', 'vnet-${parSpokeName}-${varLocAbbrev}')
#disable-next-line no-hardcoded-env-urls
var varPrivateDNSZoneSqlId = resourceId(parHubSubId, parHubNetworkResourceGroupName, 'Microsoft.Network/privateDnsZones', 'privatelink.database.windows.net')
var varHubLogAnalyticsWorkspaceId = resourceId(parHubSubId, parHubManagementResourceGroupName, 'Microsoft.OperationalInsights/workspaces', parHubLogAnalyticsName)

@sys.description('Create SQL Server Object')
resource resSqlServer 'Microsoft.Sql/servers@2023-08-01' = {
  name: 'sqlsrv-${parAppName}-${parEnvironment}-${parLocAbbrev}'
  location: parLocation
  tags: union(parSqlTags, {
    environment: parEnvironment
  })
  identity: {
    type: 'SystemAssigned'
  }
  properties: {
    administrators: {
      administratorType: 'ActiveDirectory'
      azureADOnlyAuthentication: true
      login: parSecGroupNameSql
      principalType: 'Group'
      sid: parSecGroupSidSql
      tenantId: subscription().tenantId
    }
    minimalTlsVersion: '1.2'
    publicNetworkAccess: 'Disabled'
    restrictOutboundNetworkAccess: 'Disabled'
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resSqlServerLock 'Microsoft.Authorization/locks@2020-05-01' = if (parSqlServerLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resSqlServer
  name: parSqlServerLock.?name ?? '${resSqlServer.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parSqlServerLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parSqlServerLock.?notes
  }
}

@sys.description('Enable SQL Threat Detection Settings')
resource resSqlThreatDetectionSettings 'Microsoft.Sql/servers/advancedThreatProtectionSettings@2024-11-01-preview' = {
  parent: resSqlServer
  name: 'default'
  properties: {
    state: 'Enabled'
  }
}

resource resSqlServerVulnerabilityConfig 'Microsoft.Sql/servers/sqlVulnerabilityAssessments@2024-11-01-preview' = {
  parent: resSqlServer
  name: 'default'
  properties: {
    state: 'Enabled'    
  }
}

// @sys.description('Enable Advanced Security Assessment for SQL Server')
// resource resSqlServerAdvancedSecurityAssessment 'Microsoft.Sql/servers/securityAlertPolicies@2024-11-01-preview' = {
//   parent: resSqlServer
//   name: '${resSqlServer.name}-advancedSecurityAssessment'
//   properties:{
//     state: 'Enabled'
//   }
// }

@sys.description('Get Master DB to enable Server level diagnostics')
resource resSqlMasterDb 'Microsoft.Sql/servers/databases@2024-11-01-preview' = {
  parent: resSqlServer
  location: parLocation
  name: 'master'
  properties: {}
}

@sys.description('Enable SQL Security Audit Events')
resource resSqlDiagnosticSettings 'Microsoft.Insights/diagnosticSettings@2021-05-01-preview' = {
  scope: resSqlMasterDb
  name: '${resSqlServer.name}-diagnosticSettings'
  properties: {
    workspaceId: varHubLogAnalyticsWorkspaceId
    logs: [
      {
        category: 'SQLSecurityAuditEvents'
        enabled: true
      }
    ]
  }
}

@sys.description('Enable SQL Auditing Settings')
resource resSqlAuditingSettings 'Microsoft.Sql/servers/auditingSettings@2024-11-01-preview' = {
  parent: resSqlServer
  name: 'default'
  properties: {
    auditActionsAndGroups: [
      'BATCH_COMPLETED_GROUP'
      'SUCCESSFUL_DATABASE_AUTHENTICATION_GROUP'
      'FAILED_DATABASE_AUTHENTICATION_GROUP'
    ]
    state: 'Enabled'
    isAzureMonitorTargetEnabled: true
  }
}

@sys.description('Create SQL Database Production')
resource resSqlDBprd 'Microsoft.Sql/servers/databases@2023-08-01' = {
  name: 'sqldb-${parAppName}-${(parEnvironment == 'dev' ? 'tst' : 'prd')}'
  parent: resSqlServer
  location: parLocation
  sku: {
    name: parSqlSkuNamePrd
    family: parSqlSkuFamilyPrd
    size: parSqlSkuSizePrd
    tier: parSqlSkuTierPrd
    capacity: parSqlSkuCapacityPrd
  }
  tags: union(parSqlTags, {
    workloadtype: 'database'
    environment: parEnvironment
  })
  properties: {
    collation: 'SQL_Latin1_General_CP1_CI_AS'
    maxSizeBytes: parSqlMaxSizeBytesPrd
    requestedBackupStorageRedundancy: 'Geo'
    zoneRedundant: parSqlZoneRedundantPrd
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resSqlDBprdLock 'Microsoft.Authorization/locks@2020-05-01' = if (parSqlDBprdLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resSqlDBprd
  name: parSqlDBprdLock.?name ?? '${resSqlDBprd.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parSqlDBprdLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parSqlDBprdLock.?notes
  }
}
 
@sys.description('Create SQL Database Acceptance')
resource resSqlDBacc 'Microsoft.Sql/servers/databases@2023-08-01' = {
  name: 'sqldb-${parAppName}-${(parEnvironment == 'dev' ? 'dev' : 'acc')}'
  parent: resSqlServer
  location: parLocation
  sku: {
    name: parSqlSkuNameAcc
    family: parSqlSkuFamilyAcc
    size: parSqlSkuSizeAcc
    tier: parSqlSkuTierAcc
    capacity: parSqlSkuCapacityAcc
  }
  tags: union(parSqlTags, {
    workloadtype: 'database'
    environment: parEnvironment
  })
  properties: {
    collation: 'SQL_Latin1_General_CP1_CI_AS'
    maxSizeBytes: parSqlMaxSizeBytesAcc
    requestedBackupStorageRedundancy: 'Local'
    zoneRedundant: parSqlZoneRedundantAcc
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resSqlDBaccLock 'Microsoft.Authorization/locks@2020-05-01' = if (parSqlDBaccLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resSqlDBacc
  name: parSqlDBaccLock.?name ?? '${resSqlDBacc.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parSqlDBaccLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parSqlDBaccLock.?notes
  }
}

@sys.description('Configure short term retention policy for production databaes')
resource resSqlDBprdShortTermRetention 'Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies@2024-11-01-preview' = if (parEnvironment == 'prd') {
  name: 'Default'
  parent: resSqlDBprd
  properties: {
    retentionDays: 35
    diffBackupIntervalInHours: 24
  }
}

@sys.description('Configure long term retention policy for production databaes')
resource resSqlDBprdLongTermRetention 'Microsoft.Sql/servers/databases/backupLongTermRetentionPolicies@2024-11-01-preview' = if (parEnvironment == 'prd') {
  name: 'Default'
  parent: resSqlDBprd
  properties: {
    weeklyRetention: 'P5W'
    monthlyRetention: 'P12W'
    yearlyRetention: 'P2W'
    weekOfYear: 1
  }
}

@sys.description('Create Private Endpoint for SQL Server Object in PCP network')
resource resSqlSrvPrivateEndpoint01 'Microsoft.Network/privateEndpoints@2024-07-01' = {
  name: 'pep-${resSqlServer.name}'
  location: parLocation
  tags: union(parSqlTags, {
    workloadtype: 'networking'
    environment: parEnvironment
  })
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'sqlServer'
          memberName: 'sqlServer'
          privateIPAddress: parSqlIpAddress
        }
      }
    ]
    subnet: {
      id: varCustomAppsSubnetId
    }
    privateLinkServiceConnections: [
      {
        name: 'pep-${resSqlServer.name}'
        properties: {
          privateLinkServiceId: resSqlServer.id
          groupIds: [
            'sqlServer'
          ]
        }
      }
    ]
  }
}

@sys.description('Create a resource lock for the SQL Server private endpoint if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resSqlSrvPrivateEndpoint01Lock 'Microsoft.Authorization/locks@2020-05-01' = if (parSqlSrvPrivateEndpoint01Lock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resSqlSrvPrivateEndpoint01
  name: parSqlSrvPrivateEndpoint01Lock.?name ?? '${resSqlSrvPrivateEndpoint01.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parSqlSrvPrivateEndpoint01Lock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parSqlSrvPrivateEndpoint01Lock.?notes
  }
}

@sys.description('Create Private DNS Zone Group for SQL Server Object')
resource resPrivateDNSZoneGroup 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2024-07-01' = {
  parent: resSqlSrvPrivateEndpoint01
  name: 'dnsgroupname'
  properties: {
    privateDnsZoneConfigs: [
      {
        name: 'config1'
        properties: {
          privateDnsZoneId: varPrivateDNSZoneSqlId
        }
      }
    ]
  }
}

@sys.description('Link DNS Zone in CustomApps Network')
module sqlDNSLink '../../subModules/privateDNSLink/privateDNSLink.bicep' = if (!empty(parHubSubId)) {
  name: 'sqlDNSLink'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parSpoke: parSpokeName
    parPrivateDnsZoneName: parPrivateDnsZoneSqlName
    parVirtualNetworkIdToLink: varVirtualNetworkIdToLink
  }
  dependsOn: [
    resSqlSrvPrivateEndpoint01
  ]
}

output outSqlServerName string = resSqlServer.name
