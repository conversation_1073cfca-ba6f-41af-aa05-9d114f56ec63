@description('Main parameters')
param parLocation string
param parLocAbbrev string
param parCompanyPrefix string
param parAppName string
param parSpokeName string
param parEnvironment string
param parDeployScriptsIdentity string
param parDeploymentScriptSaName string
param parSQLserverName string
param parDBRoleNames string = 'db_datareader--db_datawriter--db_ddladmin'
param parCurrentTime string
param parPCPSubId string
param parHubSubId string
param parHubIDResourceGroupName string

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varContainerInstanceSubnet = resourceId(parPCPSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'containerInstanceSubnet')
var varUaidentity = resourceId(parHubSubId, parHubIDResourceGroupName, 'Microsoft.ManagedIdentity/userAssignedIdentities', parDeployScriptsIdentity)

var sqlServerHostname = environment().suffixes.sqlServerHostname
var instance = '${parSQLserverName}${sqlServerHostname}'
var tokenResourceUrl = 'https://${substring(sqlServerHostname, 1)}'

@description('get Azure Data Factory resource for its principalId')
resource resAzureDataFactory 'Microsoft.DataFactory/factories@2018-06-01' existing = {
  name: 'adf-${parAppName}-${(parEnvironment == 'dev' ? 'dev' : 'prd')}-${parLocAbbrev}'
}

resource deploymentScriptSQLDBAfdAssignmentPrd 'Microsoft.Resources/deploymentScripts@2023-08-01' = {
  name: 'SQLDBAfdPermissions${(parEnvironment == 'dev' ? 'tst' : 'prd')}-${guid(parAppName)}'
  location: parLocation
  kind: 'AzurePowerShell'
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${varUaidentity}': {}
    }
  }
  properties: {
    azPowerShellVersion: '11.0'
    storageAccountSettings: {
      storageAccountName: parDeploymentScriptSaName
    }
    containerSettings: {
      subnetIds: [
        {
          id: varContainerInstanceSubnet
        }
      ]
    }
    arguments: '-instance "${instance}" -tokenResourceUrl "${tokenResourceUrl}" -dbName "sqldb-${parAppName}-${(parEnvironment == 'dev' ? 'tst' : 'prd')}" -adObjectName "${resAzureDataFactory.name}" -dbRoleNames "${parDBRoleNames}"'
    scriptContent: '''
      param(
        [string] $instance,
        [string] $tokenResourceUrl,
        [string] $dbName,
        [string] $adObjectName,
        [string] $dbRoleNames
      )

      Install-Module -Name SqlServer -Force
      Import-Module -Name SqlServer
      $token = (Get-AzAccessToken -ResourceUrl $tokenResourceUrl).Token
      $roles = $dbRoleNames.Split("--")
      $sqlStatements = @()
      $sqlStatements += "CREATE USER `"$adObjectName`" FROM external provider"
      foreach($role in $roles){
        $sqlStatements += "ALTER ROLE $role ADD MEMBER `"$adObjectName`""
      }
      $query = $sqlStatements -join ";"
      Invoke-Sqlcmd -ServerInstance $instance -Database $dbName -AccessToken "$token" -Query $query
    '''
    retentionInterval: 'PT1H'
    cleanupPreference: 'Always'
    forceUpdateTag: parCurrentTime // ensures script will run every time
  }
}

resource deploymentScriptSQLDBAfdAssignmentAcc 'Microsoft.Resources/deploymentScripts@2023-08-01' = {
  name: 'SQLDBAfdPermissions${(parEnvironment == 'dev' ? 'dev' : 'acc')}-${guid(parAppName)}'
  location: parLocation
  kind: 'AzurePowerShell'
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${varUaidentity}': {}
    }
  }
  properties: {
    azPowerShellVersion: '11.0'
    storageAccountSettings: {
      storageAccountName: parDeploymentScriptSaName
    }
    containerSettings: {
      subnetIds: [
        {
          id: varContainerInstanceSubnet
        }
      ]
    }
    arguments: '-instance "${instance}" -tokenResourceUrl "${tokenResourceUrl}" -dbName "sqldb-${parAppName}-${(parEnvironment == 'dev' ? 'dev' : 'acc')}" -adObjectName "${resAzureDataFactory.name}" -dbRoleNames "${parDBRoleNames}"'
    scriptContent: '''
      param(
        [string] $instance,
        [string] $tokenResourceUrl,
        [string] $dbName,
        [string] $adObjectName,
        [string] $dbRoleNames
      )

      Install-Module -Name SqlServer -Force
      Import-Module -Name SqlServer
      $token = (Get-AzAccessToken -ResourceUrl $tokenResourceUrl).Token
      $roles = $dbRoleNames.Split("--")
      $sqlStatements = @()
      $sqlStatements += "CREATE USER `"$adObjectName`" FROM external provider"
      foreach($role in $roles){
        $sqlStatements += "ALTER ROLE $role ADD MEMBER `"$adObjectName`""
      }
      $query = $sqlStatements -join ";"
      Invoke-Sqlcmd -ServerInstance $instance -Database $dbName -AccessToken "$token" -Query $query
    '''
    retentionInterval: 'PT1H'
    cleanupPreference: 'Always'
    forceUpdateTag: parCurrentTime // ensures script will run every time
  }
  dependsOn:[
    deploymentScriptSQLDBAfdAssignmentPrd
    ]
}
