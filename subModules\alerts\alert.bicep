@description('Name of the landing zone/application.')
param parAppName string

@description('Type of the application (api/clt/mgt).')
param parResourceType string

@description('Condition identifier for the alert (e.g., available, healthcheck).')
param parCondition string

@description('Description of what the alert monitors.')
param parAlertDescription string
@description('Type of alert to create. HealthCheck monitors app service health check endpoint.')
@allowed([
  'Metric'
  'Log'
  'WebTest'
  'HealthCheck'
])
param parAlertType string

@description('Standard criteria for health check alerts that monitors the app service health check endpoint.')
var varHealthCheckCriteria = {
  allOf: [
    {
      threshold: 90
      name: 'Metric1'
      metricNamespace: contains(parTargetResourceId, '/slots/') ? 'microsoft.web/sites/slots' : 'microsoft.web/sites'
      metricName: 'HealthCheckStatus'
      operator: 'LessThan'
      timeAggregation: 'Average'
      criterionType: 'StaticThresholdCriterion'
    }
  ]
  'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
}

@description('The webtest resource ID for WebTest alerts')
param parWebTestId string = ''

@description('Name of the alert, constructed from app name, type, and condition.')
var varAlertName = 'alert-${parAppName}-${parResourceType}-${parCondition}'
@description('Severity level of the alert (0-4). Default is 1 (Error).')
param parAlertSeverity int = 1

@description('Whether the alert is enabled.')
param parEnabled bool = true

@description('Evaluation frequency of the alert. Default is PT5M (5 minutes).')
param parEvaluationFrequency string = 'PT5M'

@description('The time window over which the values are aggregated. Default is PT5M (5 minutes).')
param parWindowSize string = 'PT5M'

@description('Whether to automatically resolve the alert when the condition is no longer met.')
param parAutoMitigate bool = true

@description('Resource ID of the target resource to monitor.')
param parTargetResourceId string

param parActionGroupDevId string

param parActionGroupOpsId string

@allowed([
  'devteam'
  'opsteam'
])
param parActionGroupType string

@description('Alert criteria configuration. Not used for HealthCheck alerts which use standard criteria.')
param parAlertCriteria object = {}

@description('Location for the alert resource. Not used for metric alerts.')
param parLocation string

var varActionGroupId = parActionGroupType == 'devteam' ? parActionGroupDevId : parActionGroupOpsId

@description('Array of actions to execute when the alert is triggered.')
var varActions = [
      {
        actionGroupId: varActionGroupId
        webHookProperties: {}
      }
    ]

@description('Array of action group IDs for log alerts.')
var varActionGroups = [varActionGroupId]

// Metric Alert
resource resMetricAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = if (parAlertType == 'Metric' || parAlertType == 'HealthCheck') {
  name: varAlertName
  location: 'global'
  properties: {
    description: parAlertDescription
    severity: parAlertSeverity
    enabled: parEnabled
    scopes: [
      parTargetResourceId
    ]
    evaluationFrequency: parEvaluationFrequency
    windowSize: parWindowSize
    autoMitigate: parAutoMitigate
    criteria: parAlertType == 'HealthCheck' ? varHealthCheckCriteria : parAlertCriteria
    actions: varActions
  }
}

// Log Alert
resource resLogAlert 'Microsoft.Insights/scheduledQueryRules@2024-01-01-preview' = if (parAlertType == 'Log') {
  name: varAlertName
  location: parLocation
  kind: 'LogAlert'
  properties: {
    displayName: varAlertName
    description: parAlertDescription
    severity: parAlertSeverity
    enabled: parEnabled
    evaluationFrequency: parEvaluationFrequency
    scopes: [
      parTargetResourceId
    ]
    targetResourceTypes: [
      'microsoft.insights/components'
    ]
    windowSize: parWindowSize
    criteria: parAlertCriteria
    autoMitigate: parAutoMitigate
    actions: {
      actionGroups: varActionGroups
      customProperties: {}
      actionProperties: {}
    }
  }
}

// WebTest Alert
resource resWebTestAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = if (parAlertType == 'WebTest') {
  name: varAlertName
  location: 'global'
  tags: {
    'hidden-link:${parTargetResourceId}': 'Resource'
    'hidden-link:${parWebTestId}': 'Resource'
  }
  properties: {
    description: parAlertDescription
    severity: parAlertSeverity
    enabled: parEnabled
    scopes: [
      parWebTestId
      parTargetResourceId
    ]
    evaluationFrequency: parEvaluationFrequency
    windowSize: parWindowSize
    criteria: {
      webTestId: parWebTestId
      componentId: parTargetResourceId
      failedLocationCount: 2
      'odata.type': 'Microsoft.Azure.Monitor.WebtestLocationAvailabilityCriteria'
    }
    autoMitigate: parAutoMitigate
    actions: varActions
  }
}
