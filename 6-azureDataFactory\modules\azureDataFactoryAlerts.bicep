@description('Abbreviation of the Azure region.')
param parLocAbbrev string
@description('Azure region where resources should be deployed.')
param parLocation string
@description('Name of the landing zone/application.')
param parAppName string
@description('Deployment environment (dev/prd).')
param parEnvironment string

param parActionGroupDevId string
param parActionGroupOpsId string
param parActionGroupTypeAcc string
param parActionGroupTypePrd string

resource resAzureDataFactory 'Microsoft.DataFactory/factories@2018-06-01' existing = {
  name: 'adf-${parAppName}-${parEnvironment}-${parLocAbbrev}'
}

var varEnvironments = parEnvironment == 'dev' ? [
  {
    actionGroupType: parActionGroupTypeAcc
    envName: 'dev'
    pipelineEnvName: 'ACC'
  }
  {
    actionGroupType: parActionGroupTypePrd
    envName: 'tst'
    pipelineEnvName: 'PRD'
  }
] : [
  {
    actionGroupType: parActionGroupTypeAcc
    envName: 'acc'
    pipelineEnvName: 'ACC'
  }
  {
    actionGroupType: parActionGroupTypePrd
    envName: 'prd'
    pipelineEnvName: 'PRD'
  }
]

// Pipeline Failed Alert - ACC Environment
module modPipelineFailedAlert '../../subModules/alerts/alert.bicep' = [for env in varEnvironments: {
  name: 'pipelineFailedAlert-${parAppName}-${env.envName}'
  params: {
    parAppName: parAppName
    parResourceType: 'adf'
    parCondition: 'pipeline-failed-${env.envName}'
    parAlertDescription: 'Alert when any pipeline fails in ${env.envName} environment'
    parAlertSeverity: 1
    parEnabled: true
    parEvaluationFrequency: 'PT1M'
    parWindowSize: 'PT5M'
    parTargetResourceId: resAzureDataFactory.id
    parActionGroupDevId: parActionGroupDevId
    parActionGroupOpsId: parActionGroupOpsId
    parActionGroupType: env.actionGroupType
    parLocation: parLocation
    parAlertType: 'Metric'
    parAlertCriteria: {
      allOf: [
        {
          threshold: 0
          name: 'Metric1'
          metricNamespace: 'Microsoft.DataFactory/factories'
          metricName: 'PipelineFailedRuns'
          dimensions: [
            {
              name: 'Name'
              operator: 'Include'
              values: [
                'PCP_${env.pipelineEnvName}_DYN_PIPELINE'
                'GET_${env.pipelineEnvName}_Tablenames_Metadata'
              ]
            }
          ]
          operator: 'GreaterThan'
          timeAggregation: 'Total'
          skipMetricValidation: false
          criterionType: 'StaticThresholdCriterion'
        }
      ]
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
    }
  }
}]

// Pipeline Not Running Alert
module modPipelineNotRunningAlert '../../subModules/alerts/alert.bicep' = [ for env in varEnvironments: if (parEnvironment != 'dev')   {
  name: 'pipelineNotRunningAlert-${parAppName}-${env.envName}'
  params: {
    parAppName: parAppName
    parResourceType: 'adf'
    parCondition: 'pipeline-notrunning-${env.envName}'
    parAlertDescription: 'Alert when pcp pipeline is not running in ${env.envName} environment'
    parAlertSeverity: 1
    parEnabled: true
    parEvaluationFrequency: 'PT1H'
    parWindowSize: 'P1D'
    parTargetResourceId: resAzureDataFactory.id
    parActionGroupDevId: parActionGroupDevId
    parActionGroupOpsId: parActionGroupOpsId
    parActionGroupType: env.actionGroupType
    parLocation: parLocation
    parAlertType: 'Metric'
    parAlertCriteria: {
      allOf: [
        {
          threshold: 1
          name: '15dd58f1-5326-4ace-8824-832f4a6206bf'
          metricNamespace: 'Microsoft.DataFactory/factories'
          metricName: 'TriggerSucceededRuns'
          dimensions: [
            {
              name: 'Name'
              operator: 'Include'
              values: [
                'PCP_${env.pipelineEnvName}_DYN_PIPELINE'
                'GET_${env.pipelineEnvName}_Tablenames_Metadata'
              ]
            }
            {
              name: 'FailureType'
              operator: 'Include'
              values: [
                'none'
              ]
            }
          ]
          operator: 'LessThan'
          timeAggregation: 'Total'
          criterionType: 'StaticThresholdCriterion'
        }
      ]
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
    }
  }
}
]
