metadata name = 'ALZ Bicep - PCP Application Infrastructure - Storage Accounts'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure Storage Accounts'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType

param parLocation string
param parLocAbbrev string
param parCompanyPrefix string
param parSpokeName string
@minLength(3)
param parEnv string
param parTagsSa object = {}
param parSpokeSubId string
param parHubSubId string
param parHubNetworkResourceGroupName string
param parSpokeNetworkAddressOctets string
#disable-next-line no-hardcoded-env-urls 
param parPrivateDNSZoneSa string = 'privatelink.blob.core.windows.net'
param parContainerList array

param parAppStorageAcctLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure Storage Account.'
}

param parSaPrivateEndpoint01Lock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure Storage Account.'
}

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varCustomAppsSubnetId = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'storageSubnet')
var varVirtualNetworkIdToLink = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks', 'vnet-${parSpokeName}-${varLocAbbrev}')
#disable-next-line no-hardcoded-env-urls 
var varPrivateDnsZoneNameSa = resourceId(parHubSubId, parHubNetworkResourceGroupName, 'Microsoft.Network/privateDnsZones', 'privatelink.blob.core.windows.net')

var varRoleDefinitionId = subscriptionResourceId(
  'Microsoft.Authorization/roleDefinitions',
  'e5e2a7ff-d759-4cd2-bb51-3152d37e2eb1'
)

resource resAppStorageAcct 'Microsoft.Storage/storageAccounts@2025-01-01' = {
  name: toLower('sa${parEnv}${replace(parSpokeName, '-', '')}${parLocAbbrev}')
  location: parLocation
  tags: union(parTagsSa, {
    workloadtype: 'storage'
  })
  sku: {
    name: 'Standard_GRS'
  }
  kind: 'StorageV2'
  properties: {
    accessTier: 'Hot'
    allowBlobPublicAccess: false
    allowCrossTenantReplication: true
    allowedCopyScope: 'PrivateLink'
    allowSharedKeyAccess: false
    isSftpEnabled: false
    minimumTlsVersion:'TLS1_2'
    publicNetworkAccess: 'Disabled'
    supportsHttpsTrafficOnly: true
  }
}

@description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAppStorageAcctLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAppStorageAcctLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resAppStorageAcct
  name: parAppStorageAcctLock.?name ?? '${resAppStorageAcct.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAppStorageAcctLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAppStorageAcctLock.?notes
  }
}

resource resSaPrivateEndpoint01 'Microsoft.Network/privateEndpoints@2024-07-01' = {
  name: 'pep-blob-${resAppStorageAcct.name}'
  location: parLocation
  tags: union(parTagsSa, {
    workloadtype: 'networking'
  })
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'blob'
          memberName: 'blob'
          privateIPAddress: (parEnv == 'tst' || parEnv == 'prd' ? '${parSpokeNetworkAddressOctets}.4.4' : '${parSpokeNetworkAddressOctets}.4.5')
        }
      }
    ]
    subnet: {
      id: varCustomAppsSubnetId
    }
    privateLinkServiceConnections: [
      {
        name: 'pep-blob-${resAppStorageAcct.name}'
        properties: {
          privateLinkServiceId: resAppStorageAcct.id
          groupIds: [
            'blob'            
          ]
        }
      }
    ]
  }
}

@description('Create a resource lock for the Storage accounts private endpoint if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resSaPrivateEndpoint01Lock 'Microsoft.Authorization/locks@2020-05-01' = if (parSaPrivateEndpoint01Lock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resSaPrivateEndpoint01
  name: parSaPrivateEndpoint01Lock.?name ?? '${resSaPrivateEndpoint01.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parSaPrivateEndpoint01Lock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parSaPrivateEndpoint01Lock.?notes
  }
}

resource resPrivateDNSZoneGroup 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2023-09-01' = {
  parent: resSaPrivateEndpoint01
  name: 'dnsgroupname'
  properties: {
    privateDnsZoneConfigs: [
      {
        name: 'config1'
        properties: {
          privateDnsZoneId: varPrivateDnsZoneNameSa
        }
      }
    ]
  }
}

module saDNSLink '../../subModules/privateDNSLink/privateDNSLink.bicep' = if (parEnv == 'prd' || parEnv == 'tst') {
  name: 'saDNSLink'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parSpoke: toLower(parSpokeName)
    parPrivateDnsZoneName: parPrivateDNSZoneSa
    parVirtualNetworkIdToLink: varVirtualNetworkIdToLink
  }
}

resource resblobServices 'Microsoft.Storage/storageAccounts/blobServices@2023-05-01' = if (parEnv == 'prd') {
  name: 'default'
  parent: resAppStorageAcct
}

resource resContainers 'Microsoft.Storage/storageAccounts/blobServices/containers@2023-05-01' = [for containerName in parContainerList : if (parEnv == 'prd') {
  name: containerName
  parent: resblobServices
  properties: {
    publicAccess: 'None'
    metadata: {}
  }
}]

resource resBackupVault 'Microsoft.DataProtection/backupVaults@2024-04-01' existing = {
  name: 'bvault-${parSpokeName}-${parLocAbbrev}'
  scope: resourceGroup(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-sharedservices-${parLocAbbrev}')
}

resource resRoleAssignment 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  scope: resAppStorageAcct
  name: guid(resBackupVault.id, varRoleDefinitionId, resAppStorageAcct.id)
  properties: {
    roleDefinitionId: varRoleDefinitionId
    principalId: resBackupVault.identity.principalId
    principalType: 'ServicePrincipal'
  }
}

module modStorageBackupInstance 'storageBackupInstance.bicep' = if (parEnv == 'prd') {
  name: 'storageBackupInstance${guid(resAppStorageAcct.id)}'
  scope: resourceGroup(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-sharedservices-${parLocAbbrev}')
  params: {
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parSpokeName: parSpokeName
    parCompanyPrefix: parCompanyPrefix
    parSpokeSubId: parSpokeSubId
    parStorageAccountName: resAppStorageAcct.name
    parContainerList: parContainerList
  }
  dependsOn: [
    resRoleAssignment
  ]
}
