metadata name = '<PERSON><PERSON> Bicep - PCP Application Infrastructure - SQL Databases'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure SQL Databases'

param parLocation string
param parLocAbbrev string
param parCompanyPrefix string
param parSpokeName string
param parDeployScriptsIdentity string
param parDeploymentScriptSaName string
param parSqlServerName string
param parHubSubId string
param parSpokeSubId string
param parAppResourceGroupName string
param parHubIdResourceGroupName string
param parDirReaderGroupSid string
param parCurrentTime string = utcNow()

var varContainerInstanceSubnet = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${parLocAbbrev}', 'containerInstanceSubnet')
var varUaidentity = resourceId(parHubSubId, parHubIdResourceGroupName, 'Microsoft.ManagedIdentity/userAssignedIdentities', parDeployScriptsIdentity)

@sys.description('Get the SQL Server to be given the Azure AD Group Reader role')
resource resSqlServer 'Microsoft.Sql/servers@2023-08-01-preview' existing = {
  name: parSqlServerName
  scope: resourceGroup(parSpokeSubId, parAppResourceGroupName)
}

@sys.description('Create the deployment script to assign the Azure AD Group Reader role to the SQL Server')
resource deploymentScriptServerGroupAssignmentPrd 'Microsoft.Resources/deploymentScripts@2023-08-01' = {
  name: 'SqlServer2AzureADGroupPrd-${uniqueString(resourceGroup().id)}'
  location: parLocation
  kind: 'AzurePowerShell'
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${varUaidentity}': {}
    }
  }
  properties: {
    azPowerShellVersion: '11.0'
    storageAccountSettings: {
      storageAccountName: parDeploymentScriptSaName
    }
    containerSettings: {
      subnetIds: [
        {
          id: varContainerInstanceSubnet
        }
      ]
    }
    retentionInterval: 'PT1H'
    arguments: '-servicePrincipalId "${resSqlServer.identity.principalId}" -groupId "${parDirReaderGroupSid}"'
    scriptContent: '''
      param(
        [string] $servicePrincipalId,
        [string] $groupId
      )
      $token = (Get-AzAccessToken -ResourceUrl https://graph.microsoft.com).Token
      $headers = @{'Content-Type' = 'application/json'; 'Authorization' = 'Bearer ' + $token }

      $url = "https://graph.microsoft.com/beta/groups/$groupId/members/`$ref"

      $checkMember = Invoke-RestMethod -Uri $url -Headers $headers -Method Get -ContentType "application/json"
      $memberResults = $checkMember.value

      Foreach ($member in $memberResults) {
          if ($member -like "*$servicePrincipalId*") {
              $state = "alreadyMember"
          }
      }
        
      if ($state -ne "alreadyMember") {
          $body = @{
          "@odata.id" = "https://graph.microsoft.com/beta/directoryObjects/$servicePrincipalId"
      }
          $url = "https://graph.microsoft.com/beta/groups/$groupId/members/`$ref"
          $result = Invoke-RestMethod -Method POST -Headers $headers -Uri $url -Body ($body | ConvertTo-Json)
      }
    '''
    cleanupPreference: 'Always'
    forceUpdateTag: parCurrentTime // ensures script will run every time
  }
}
