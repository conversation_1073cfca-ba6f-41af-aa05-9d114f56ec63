@description('Logic App HTTP trigger callback URL (the long .../triggers/manual/... URL)')
@secure()
param parTeamsChannelLogicAppTriggerUrl string

@description('Short name of the action group (max 12 characters)')
param parActionGroupShortName string

@description('Display name for the action group')
param parActionGroupDisplayName string

resource actionGroup 'Microsoft.Insights/actionGroups@2022-06-01' = {
  name: parActionGroupDisplayName
  location: 'Global'
  properties: {
    groupShortName: parActionGroupShortName
    enabled: true

    // Post alerts directly to your Logic App trigger URL
    webhookReceivers: [
      {
        name: 'logicAppWebhook'
        serviceUri: parTeamsChannelLogicAppTriggerUrl
        // Send Azure Monitor Common Alert Schema to your Logic App
        useCommonAlertSchema: true
      }
    ]
  }
}

output actionGroupId string = actionGroup.id
