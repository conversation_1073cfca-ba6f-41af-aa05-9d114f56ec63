@description('Abbreviation of the Azure region.')
param parLocAbbrev string

@description('Azure region where resources should be deployed.')
param parLocation string

@description('Name of the landing zone/application.')
param parAppName string

@description('Deployment environment (dev/prd).')
param parEnvironment string

@description('Type of the application (api/clt/mgt).')
param parAppType string

@description('Array of alerts to create. Each alert should specify type (Metric/Log/WebTest/HealthCheck) and required configuration.')
param parAlerts array = []

param parActionGroupDevId string
param parActionGroupOpsId string

resource resApplicationInsights 'Microsoft.Insights/components@2020-02-02' existing = {
  name: 'appi-${parAppName}-${parAppType}-${parEnvironment}-${parLocAbbrev}'
}

// Create alerts
module modAlerts '../../subModules/alerts/alert.bicep' = [for alert in parAlerts: {
  name: 'alert-${alert.name}'
  params: {
    parAppName: parAppName
    parResourceType: parAppType
    parCondition: alert.condition
    parAlertDescription: alert.description
    parAlertSeverity: alert.severity
    parEnabled: true
    parTargetResourceId: alert.?targetResourceId ?? resApplicationInsights.id
    parActionGroupType: alert.actionGroupType
    parActionGroupDevId: parActionGroupDevId
    parActionGroupOpsId: parActionGroupOpsId
    parAlertType: alert.alertType
    parWebTestId: alert.?webTestId ?? ''
    parAlertCriteria: alert.?criteria
    parLocation: parLocation
  }
}]
