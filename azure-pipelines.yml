trigger: none

name: ${{ parameters.parEnvironment }}_$(Date:yyyyMMdd)$(Rev:.r)

pool:
  name: TT PCP - WindowsAgents prd

parameters:
- name: parEnvironment
  type: string
  default: dev
  values:
    - dev
    - prd2
    - prd

variables:
- group: ${{ parameters.parEnvironment }}VariableGroupTridentPCP
- name: varServiceConnectionName
  value: ${{ parameters.parEnvironment }} Infra Service Connection
- name: RunNumber
  value: ${{ parameters.parEnvironment }}-$(Build.BuildNumber)

stages:
- stage: deployInfrastructure  
  displayName: Stage - Deploy Infrastructure
  dependsOn: initializeVariables
  jobs:
  - job: deployAllResources
    displayName: Job - Deploy All Infrastructure Resources
    steps:
    - task: AzureCLI@2
      displayName: Deploy All Infrastructure
      name: deploy_infrastructure
      inputs:
        azureSubscription: $(varServiceConnectionName)
        scriptType: pscore
        scriptLocation: inlineScript
        inlineScript: |
          az account set --subscription $(varPCPSubId)
          az deployment sub create `
          --template-file main.bicep `
          --parameters main.${{ parameters.parEnvironment }}.bicepparam `
          --parameters parApiClientSecretAcc=$(varApiClientSecretAcc) `
          --parameters parApiClientSecretPrd=$(varApiClientSecretPrd) `
          --parameters parApiSmtpPasswordAcc=$(varApiSmtpPasswordAcc) `
          --parameters parApiSmtpPasswordPrd=$(varApiSmtpPasswordPrd) `
          --parameters parCltClientSecretAcc=$(varCltClientSecretAcc) `
          --parameters parCltClientSecretExternalIdAcc=$(varCltClientSecretExternalIdAcc) `
          --parameters parCltClientSecretExternalIdPrd=$(varCltClientSecretExternalIdPrd) `
          --parameters parCltClientSecretPrd=$(varCltClientSecretPrd) `
          --parameters parMgtClientSecretAcc=$(varMgtClientSecretAcc) `
          --parameters parMgtClientSecretPrd=$(varMgtClientSecretPrd) `
          --parameters parMgtSessionSecretAcc=$(varMgtSessionSecretAcc) `
          --parameters parMgtSessionSecretPrd=$(varMgtSessionSecretPrd) `
          --location $(varLocation) `
          --name deploy_infrastructure-$(RunNumber)
