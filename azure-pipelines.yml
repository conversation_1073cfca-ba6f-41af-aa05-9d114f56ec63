trigger: none

name: ${{ parameters.parEnvironment }}_$(Date:yyyyMMdd)$(Rev:.r)

pool:
  name: TT PCP - WindowsAgents prd

parameters:
- name: parEnvironment
  type: string
  default: 'dev'
  values:
    - 'dev'
    - 'prd'
- name: parTags
  type: object
  default:
    workloadname: Private Client Portal
    costcenter: it
    managedby: netpromanagedservices
- name: parAppName
  type: string
  default: pcp

variables:
- group: ${{ parameters.parEnvironment }}VariableGroupTridentPCP
- name: varServiceConnectionName
  value: "${{ parameters.parEnvironment }} Infra Service Connection"
- name: varEnvironment
  value: ${{ parameters.parEnvironment }}
- name: varAppName
  value: ${{ parameters.parAppName }}
- name: varSpokeName
  value: "pcp"
- name: RunNumber
  value: ${{ parameters.parEnvironment }}-$(Build.BuildNumber)

stages:
- stage: initializeVariables
  displayName: Initialize Variables
  jobs:
  - job: setVariables
    displayName: Set Environment Variables
    steps:
    - pwsh: |
        "Setting Environment Variables"
        $parEnvironment = '${{ parameters.parEnvironment }}'

        if ($parEnvironment -eq 'dev') {
          Write-Host "##vso[task.setvariable variable=varCompanyPrefix;isOutput=true]npdev"
          Write-Host "##vso[task.setvariable variable=varHubNetworkingResourceGroupName;isOutput=true]npdev_hub_networking"
          Write-Host "##vso[task.setvariable variable=varHubIdResourceGroupName;isOutput=true]npdev_hub_identity"
          Write-Host "##vso[task.setvariable variable=varHubManagementResourceGroupName;isOutput=true]npdev_hub_management"
          Write-Host "##vso[task.setvariable variable=varAlertActionGroupName;isOutput=true]Peter Test"
          Write-Host "##vso[task.setvariable variable=varAlertActionGroupResourceGroupName;isOutput=true]peterheesbeentest"
        } else {
          Write-Host "##vso[task.setvariable variable=varCompanyPrefix;isOutput=true]ttg"
          Write-Host "##vso[task.setvariable variable=varHubNetworkingResourceGroupName;isOutput=true]rg-tcar-hub-networking-weu"
          Write-Host "##vso[task.setvariable variable=varHubIdResourceGroupName;isOutput=true]rg-tcar-hub-identity-weu"
          Write-Host "##vso[task.setvariable variable=varHubManagementResourceGroupName;isOutput=true]rg-tcar-hub-management-weu"
          Write-Host "##vso[task.setvariable variable=varAlertActionGroupName;isOutput=true]Email Peter Heesbeen"
          Write-Host "##vso[task.setvariable variable=varAlertActionGroupResourceGroupName;isOutput=true]rg-ttg-pcp-app-weu"
        }
      name: setEnvVars
      displayName: 'Set Environment Variables'

- stage: deployInfrastructure
  displayName: Stage - Deploy Infrastructure
  dependsOn: initializeVariables
  variables:
    - group: ${{ parameters.parEnvironment }}VariableGroupTridentPCP
    - name: varCompanyPrefix
      value: $[ stageDependencies.initializeVariables.setVariables.outputs['setEnvVars.varCompanyPrefix'] ]
    - name: varHubNetworkingResourceGroupName
      value: $[ stageDependencies.initializeVariables.setVariables.outputs['setEnvVars.varHubNetworkingResourceGroupName'] ]
    - name: varHubIdResourceGroupName
      value: $[ stageDependencies.initializeVariables.setVariables.outputs['setEnvVars.varHubIdResourceGroupName'] ]
    - name: varHubManagementResourceGroupName
      value: $[ stageDependencies.initializeVariables.setVariables.outputs['setEnvVars.varHubManagementResourceGroupName'] ]
    - name: varAlertActionGroupName
      value: $[ stageDependencies.initializeVariables.setVariables.outputs['setEnvVars.varAlertActionGroupName'] ]
    - name: varAlertActionGroupResourceGroupName
      value: $[ stageDependencies.initializeVariables.setVariables.outputs['setEnvVars.varAlertActionGroupResourceGroupName'] ]
  jobs:
  - job: deployAllResources
    displayName: Job - Deploy All Infrastructure Resources
    steps:
    - pwsh: Write-Host $(varCompanyPrefix)
      displayName: 'Show varCompanyPrefix'

    - task: AzureCLI@2
      displayName: Deploy All Infrastructure
      name: deploy_infrastructure
      inputs:
        azureSubscription: $(varServiceConnectionName)
        scriptType: 'pscore'
        scriptLocation: 'inlineScript'
        inlineScript: |
          "Setting input Variables"
          $parTags = '${{ convertToJson(parameters.parTags) }}' | ConvertFrom-Json -Depth 10
          $parEnvironment = '${{ convertToJson(parameters.parEnvironment) }}' | ConvertFrom-Json -Depth 10

          "Setting Az Deployment Variables"
          $parTagsRaw = ($parTags |  convertto-json -Depth 10 -Compress).replace('"','\"')

          "Deploying All Infrastructure using main.bicep"
          az account set --subscription $(varPCPSubId)
          az deployment sub create `
          --template-file main.bicep `
          --parameters main.${{ parameters.parEnvironment }}.bicepparam `
          --parameters parLocation=$(varLocation) parLocAbbrev=$(varLocAbbrev) parEnvironment=$parEnvironment parCompanyPrefix=$(varCompanyPrefix) parSpokeName=$(varSpokeName) `
          --parameters parAppName=$(varAppName) parSpokeSubId=$(varPCPSubId) parHubSubId=$(varHubSubId) parHubNetworkResourceGroupName=$(varHubNetworkingResourceGroupName) `
          --parameters parTenantId=$(varTenantId) `
          --parameters parMgtClientSecretPrd=$(varMgtClientSecretPrd) parMgtClientSecretAcc=$(varMgtClientSecretAcc) `
          --parameters parCltClientSecretPrd=$(varCltClientSecretPrd) parCltClientSecretAcc=$(varCltClientSecretAcc) `
          --parameters parApiClientSecretPrd=$(varApiClientSecretPrd) parApiClientSecretAcc=$(varApiClientSecretAcc) `
          --parameters parMgtSessionSecretPrd=$(varMgtSessionSecretPrd) parMgtSessionSecretAcc=$(varMgtSessionSecretAcc) `
          --parameters parCltClientSecretExternalIdPrd=$(varCltClientSecretExternalIdPrd) parCltClientSecretExternalIdAcc=$(varCltClientSecretExternalIdAcc) `
          --parameters parApiSmtpPasswordAcc=$(varApiSmtpPasswordAcc) parApiSmtpPasswordPrd=$(varApiSmtpPasswordPrd) `
          --parameters parSecGroupNameSql=$(varSecGroupNameSql) parSecGroupSidSql=$(varSecGroupSidSql) `
          --parameters parADObjectName=$(varDevOpsConnectionApp) parCustomerDomain=$(varCustomerDomain) `
          --parameters parMgtClientIdPrd=$(varMgtClientIdPrd) parMgtClientIdAcc=$(varMgtClientIdAcc) `
          --parameters parCltClientIdPrd=$(varCltClientIdPrd) parCltClientIdAcc=$(varCltClientIdAcc) `
          --parameters parApiClientIdPrd=$(varApiClientIdPrd) parApiClientIdAcc=$(varApiClientIdAcc) `
          --parameters parCltClientIdExternalIdPrd=$(varCltClientIdExternalIdPrd) parCltClientIdExternalIdAcc=$(varCltClientIdExternalIdAcc) `
          --parameters parExternalTenantId=$(varExternalTenantId) parTenantSubDomainExternalId=$(varTenantSubDomainExternalId) `
          --parameters parMongoDbNamePrd=$(varMongoDbNamePrd) parMongoDbNameAcc=$(varMongoDbNameAcc) `
          # --parameters parHubIdResourceGroupName=$(varHubIdResourceGroupName) parDeployScriptsIdentity=$(varDeployScriptsIdentity) `
          # --parameters parDeploymentScriptSaName=$(varDeploymentScriptSaName) parDirReaderGroupSid=$(varSecDirReaderGroupSid) `
          --location $(varLocation) `
          --name deploy_infrastructure-$(RunNumber)


