metadata name = 'ALZ Bicep - Trident PCP PRD2 Landing Zone'
metadata description = 'ALZ Bicep Module used to set up Trident PCP PRD2 Landing Zone'

targetScope = 'subscription'

param parGlobalResourceLock object = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep Trident PCP PRD2 Landing Zone.'
}

param parLocation string
param parLocAbbrev string
param parEnvironment string
param parCompanyPrefix string
param parSpokeName string
param parAppName string
#disable-next-line no-unused-params
param parAccEnvName string
param parSpokeSubId string
param parHubSubId string
param parHubLogAnalyticsName string
param parHubNetworkResourceGroupName string
// param parHubIdResourceGroupName string  // Temporarily disabled - only used by SQL permissions module
param parHubManagementResourceGroupName string
param parTenantId string
param parSpokeNetworkAddressOctets string
// param parDeployScriptsIdentity string  // Temporarily disabled - only used by SQL permissions module
// param parDeploymentScriptSaName string  // Temporarily disabled - only used by SQL permissions module
// param parADObjectName string // Temporarily disabled - only used by SQL permissions module
param parDevTeamChannelAlertLogicAppUrl string
param parNetProSupportEmail string
param parDevTeamActionGroupShortName string
param parDevTeamActionGroupDisplayName string
param parNetProSupportActionGroupShortName string
param parNetProSupportActionGroupDisplayName string
param parActionGroupTypePrd string
param parActionGroupTypeAcc string

@sys.description('RESOURCE GROUP SPECIFIC PARAMETERS FOR DEPLOYMENT')
param parResourceGroups array = []

@sys.description('KEY VAULT SPECIFIC PARAMETERS FOR DEPLOYMENT')
param parPrivateDNSZoneNameKeyvault string
param parKvTags object = {}
@secure()
param parMgtClientSecretPrd string
@secure()
param parMgtClientSecretAcc string
@secure()
param parCltClientSecretPrd string
@secure()
param parCltClientSecretAcc string
@secure()
param parApiClientSecretPrd string
@secure()
param parApiClientSecretAcc string
@secure()
param parMgtSessionSecretPrd string
@secure()
param parMgtSessionSecretAcc string
@secure()
param parCltClientSecretExternalIdPrd string
@secure()
param parCltClientSecretExternalIdAcc string
@secure()
param parApiSmtpPasswordAcc string
@secure()
param parApiSmtpPasswordPrd string
@secure()
param parSqlMiPcpStagingDbSecretAcc string
@secure()
param parSqlMiPcpStagingDbSecretPrd string
@secure()
param parMongoDbConnectionStringTnev string

@sys.description('STORAGE ACCOUNT SPECIFIC PARAMETERS FOR DEPLOYMENT')
param parSaTags object = {}
param parContainerList array

@sys.description('SQL DATABASES SPECIFIC PARAMETERS FOR DEPLOYMENT')
param parSecGroupNameSql string
param parSecGroupSidSql string
param parSqlSkuNamePrd string
param parSqlSkuFamilyPrd string
param parSqlSkuSizePrd string
param parSqlSkuTierPrd string
param parSqlSkuCapacityPrd int
param parSqlMaxSizeBytesPrd int
param parSqlSkuNameAcc string
param parSqlSkuFamilyAcc string
param parSqlSkuSizeAcc string
param parSqlSkuTierAcc string
param parSqlSkuCapacityAcc int
param parSqlMaxSizeBytesAcc int
param parSqlZoneRedundantPrd bool
param parSqlZoneRedundantAcc bool
param parSqlIpAddress string
// param parDirReaderGroupSid string  // Temporarily disabled - only used by SQL permissions module
param parSqlTags object = {}

@sys.description('APP SERVICES SPECIFIC PARAMETERS FOR DEPLOYMENT')
param parCustomerDomain string
param parAppTags object = {}
param parApps array = []
param parSCMIpSecurityRestrictions array = []

#disable-next-line no-unused-params
param parMgtClientIdPrd string
#disable-next-line no-unused-params
param parMgtClientIdAcc string
#disable-next-line no-unused-params
param parCltClientIdPrd string
#disable-next-line no-unused-params
param parCltClientIdAcc string
#disable-next-line no-unused-params
param parCltClientIdExternalIdPrd string
#disable-next-line no-unused-params
param parCltClientIdExternalIdAcc string
#disable-next-line no-unused-params
param parApiClientIdPrd string
#disable-next-line no-unused-params
param parApiClientIdAcc string
#disable-next-line no-unused-params
param parExternalTenantId string
#disable-next-line no-unused-params
param parTenantSubDomainExternalId string
#disable-next-line no-unused-params
param parMongoDbConnStrPrd string
#disable-next-line no-unused-params
param parMongoDbConnStrDev string
#disable-next-line no-unused-params
param parMongoDbNamePrd string
#disable-next-line no-unused-params
param parMongoDbNameAcc string

@sys.description('AZURE DATA FACTORY SPECIFIC PARAMETERS FOR DEPLOYMENT')
param parAdfTags object
param parSqlMiServerAcc string
param parSqlMiDatabaseAcc string
param parSqlMiUserNameAcc string
param parSqlMiServerPrd string
param parSqlMiDatabasePrd string
param parSqlMiUserNamePrd string

@sys.description('Module to create the resource groups')
module modResourceGroups '1-resourceGroup/resourceGroup.bicep' = [for rg in parResourceGroups: {
  name: rg.name
  params: {
    parLocation: parLocation
    parResourceGroupName: rg.name
    parTags: rg.tags
  }
}]

@sys.description('Module to create the action groups needed for alerting')
module modActionGroups './actionGroups/actionGroups.bicep' = {
  name: 'actionGroups'
  scope: resourceGroup(parResourceGroups[0].name)
  params: {
    parDevTeamChannelAlertLogicAppUrl: parDevTeamChannelAlertLogicAppUrl
    parNetProSupportEmail: parNetProSupportEmail
    parDevTeamActionGroupShortName: parDevTeamActionGroupShortName
    parDevTeamActionGroupDisplayName: parDevTeamActionGroupDisplayName
    parNetProSupportActionGroupShortName: parNetProSupportActionGroupShortName
    parNetProSupportActionGroupDisplayName: parNetProSupportActionGroupDisplayName
  }
}

var varDevTeamActionGroupId = modActionGroups.outputs.outDevTeamActionGroupId
var varOpsTeamActionGroupId = modActionGroups.outputs.outOpsTeamActionGroupId

@sys.description('Module to create the application specific key vault')
module modKeyVault '2-keyVault/keyVault.bicep' = {
  name: 'keyVault'
  scope: resourceGroup(parResourceGroups[0].name)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parCompanyPrefix: parCompanyPrefix
    parSpokeName: parSpokeName
    parAppName: parAppName
    parEnvironment: parEnvironment
    parSpokeNetworkAddressOctets: parSpokeNetworkAddressOctets
    parKvTags: parKvTags
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parSpokeSubId: parSpokeSubId
    parHubSubId: parHubSubId
    parPrivateDNSZoneNameKeyvault: parPrivateDNSZoneNameKeyvault
    parTenantId: parTenantId
    parMgtClientSecretPrd: parMgtClientSecretPrd
    parMgtClientSecretAcc: parMgtClientSecretAcc
    parCltClientSecretPrd: parCltClientSecretPrd
    parCltClientSecretAcc: parCltClientSecretAcc
    parApiClientSecretAcc: parApiClientSecretAcc
    parApiClientSecretPrd: parApiClientSecretPrd
    parMgtSessionSecretPrd: parMgtSessionSecretPrd
    parMgtSessionSecretAcc: parMgtSessionSecretAcc
    parCltClientSecretExternalIdPrd: parCltClientSecretExternalIdPrd
    parCltClientSecretExternalIdAcc: parCltClientSecretExternalIdAcc
    parApiSmtpPasswordAcc: parApiSmtpPasswordAcc
    parApiSmtpPasswordPrd: parApiSmtpPasswordPrd
    parSqlMiPcpStagingDbSecretAcc: parSqlMiPcpStagingDbSecretAcc
    parSqlMiPcpStagingDbSecretPrd: parSqlMiPcpStagingDbSecretPrd
    parMongoDbConnectionStringTnev: parMongoDbConnectionStringTnev
  }
  dependsOn: [
    modResourceGroups
  ]
}

@sys.description('Module to create the application specific storage accounts')
module modStorageAccounts '3-storageAccounts/storageAccountsMain.bicep' = {
  name: 'storageAccounts'
  scope: resourceGroup(parResourceGroups[0].name)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parCompanyPrefix: parCompanyPrefix
    parSpokeName: parSpokeName
    parSpokeSubId: parSpokeSubId
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parHubSubId: parHubSubId
    parEnvironment: parEnvironment
    parSpokeNetworkAddressOctets: parSpokeNetworkAddressOctets
    parSaTags: parSaTags
    parContainerList: parContainerList
  }
  dependsOn: [
    modResourceGroups
  ]
}

@sys.description('Module to create the application specific SQL Databases')
module modSqlDatabases '4-sqlDatabases/sqlServerMain.bicep' = {
  name: 'sqlDatabases'
  scope: resourceGroup(parResourceGroups[0].name)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    // parAppResourceGroupName: parResourceGroups[0].name
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parCompanyPrefix: parCompanyPrefix
    parAppName: parAppName
    parSpokeName: parSpokeName
    parEnvironment: parEnvironment
    parSecGroupNameSql: parSecGroupNameSql
    parSecGroupSidSql: parSecGroupSidSql
    parSqlSkuNamePrd: parSqlSkuNamePrd
    parSqlSkuFamilyPrd: parSqlSkuFamilyPrd
    parSqlSkuSizePrd: parSqlSkuSizePrd
    parSqlSkuTierPrd: parSqlSkuTierPrd
    parSqlSkuCapacityPrd: parSqlSkuCapacityPrd
    parSqlMaxSizeBytesPrd: parSqlMaxSizeBytesPrd
    parSqlSkuNameAcc: parSqlSkuNameAcc
    parSqlSkuFamilyAcc: parSqlSkuFamilyAcc
    parSqlSkuSizeAcc: parSqlSkuSizeAcc
    parSqlSkuTierAcc: parSqlSkuTierAcc
    parSqlSkuCapacityAcc: parSqlSkuCapacityAcc
    parSqlMaxSizeBytesAcc: parSqlMaxSizeBytesAcc
    parSqlZoneRedundantPrd: parSqlZoneRedundantPrd
    parSqlZoneRedundantAcc: parSqlZoneRedundantAcc
    parSqlIpAddress: parSqlIpAddress
    // parDirReaderGroupSid: parDirReaderGroupSid
    parSpokeSubId: parSpokeSubId
    parHubSubId: parHubSubId
    // parHubIdResourceGroupName: parHubIdResourceGroupName  // Temporarily disabled - only used by SQL permissions module
    parHubLogAnalyticsName: parHubLogAnalyticsName
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parHubManagementResourceGroupName: parHubManagementResourceGroupName
    // parDeployScriptsIdentity: parDeployScriptsIdentity  // Temporarily disabled - only used by SQL permissions module
    // parDeploymentScriptSaName: parDeploymentScriptSaName  // Temporarily disabled - only used by SQL permissions module
    // parAppResourceGroupName: parResourceGroups[0].name  // Temporarily disabled - only used by SQL permissions module
    // parDirReaderGroupSid: parDirReaderGroupSid  // Temporarily disabled - only used by SQL permissions module
    parSqlTags: parSqlTags
  }
  dependsOn: [
    modResourceGroups
  ]
}

@sys.description('Module to create the app services and front door configurations')
module modAppServices '5-appServices/mainAppServices.bicep' = {
  name: 'appServices'
  scope: resourceGroup(parResourceGroups[0].name)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parCompanyPrefix: parCompanyPrefix
    parSpokeName: parSpokeName
    parAppName: parAppName
    parEnvironment: parEnvironment
    parApps: parApps
    parSCMIpSecurityRestrictions: parSCMIpSecurityRestrictions
    parMgtClientIdPrd: parMgtClientIdPrd
    parMgtClientIdAcc: parMgtClientIdAcc
    parCltClientIdPrd: parCltClientIdPrd
    parCltClientIdAcc: parCltClientIdAcc
    parCltClientIdExternalIdPrd: parCltClientIdExternalIdPrd
    parCltClientIdExternalIdAcc: parCltClientIdExternalIdAcc
    parApiClientIdPrd: parApiClientIdPrd
    parApiClientIdAcc: parApiClientIdAcc
    parExternalTenantId: parExternalTenantId
    parTenantSubDomainExternalId: parTenantSubDomainExternalId
    parMongoDbConnStrPrd: parMongoDbConnStrPrd
    parMongoDbConnStrDev: parMongoDbConnStrDev
    parMongoDbNamePrd: parMongoDbNamePrd
    parMongoDbNameAcc: parMongoDbNameAcc
    parCustomerDomain: parCustomerDomain
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parHubManagementResourceGroupName: parHubManagementResourceGroupName
    parHubSubId: parHubSubId
    parSpokeSubId: parSpokeSubId
    parTenantId: parTenantId
    parAppTags: parAppTags
    parActionGroupDevId: varDevTeamActionGroupId
    parActionGroupOpsId: varOpsTeamActionGroupId
    parActionGroupTypeAcc: parActionGroupTypeAcc
    parActionGroupTypePrd: parActionGroupTypePrd
    parHubLogAnalyticsName: parHubLogAnalyticsName
  }
  dependsOn: [
    modSqlDatabases
    modStorageAccounts
    modKeyVault
  ]
}

@sys.description('Module to create the Azure Data Factory')
module modDataFactory '6-azureDataFactory/azureDataFactory.bicep' = {
  name: 'dataFactory'
  scope: resourceGroup(parResourceGroups[0].name)
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parAppName: parAppName
    parEnvironment: parEnvironment
    parAdfTags: parAdfTags
    parSqlMiDatabaseAcc: parSqlMiDatabaseAcc
    parSqlMiUserNameAcc: parSqlMiUserNameAcc
    parSqlMiServerAcc: parSqlMiServerAcc
    parSqlMiDatabasePrd: parSqlMiDatabasePrd
    parSqlMiUserNamePrd: parSqlMiUserNamePrd
    parSqlMiServerPrd: parSqlMiServerPrd
    parSpokeSubId: parSpokeSubId
    parActionGroupDevId: varDevTeamActionGroupId
    parActionGroupOpsId: varOpsTeamActionGroupId
    parActionGroupTypeAcc: parActionGroupTypeAcc
    parActionGroupTypePrd: parActionGroupTypePrd
    parCompanyPrefix: parCompanyPrefix
    parHubSubId: parHubSubId
    parHubLogAnalyticsName: parHubLogAnalyticsName
    parHubManagementResourceGroupName: parHubManagementResourceGroupName
  }
  dependsOn: [
    modSqlDatabases
    modStorageAccounts
    modKeyVault
  ]
}

// @sys.description('Module to configure SQL database permissions')
// NOTE: SQL permissions module is temporarily disabled due to functionality issues
// module modSqlPermissions '7-sqlPermissions/mainSqlDBPermissions.bicep' = {
//   name: 'sqlPermissions'
//   scope: resourceGroup(parResourceGroups[0].name)
//   params: {
//     parLocation: parLocation
//     parLocAbbrev: parLocAbbrev
//     parCompanyPrefix: parCompanyPrefix
//     parAppName: parAppName
//     parSpokeName: parSpokeName
//     parEnvironment: parEnvironment
//     parDeployScriptsIdentity: parDeployScriptsIdentity
//     parSQLserverName: modSqlDatabases.outputs.outSqlServerName
//     parDeploymentScriptSaName: parDeploymentScriptSaName
//     parADObjectName: parADObjectName
//     parHubSubId: parHubSubId
//     parPCPSubId: parSpokeSubId
//     parHubIDResourceGroupName: parHubIdResourceGroupName
//   }
//   dependsOn: [
//     modSqlDatabases
//     modAppServices
//     modDataFactory
//   ]
// }
