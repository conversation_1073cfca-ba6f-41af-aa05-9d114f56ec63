metadata name = 'ALZ Bicep - PCP Application Infrastructure - Storage Accounts'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure Storage Accounts'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType

param parLocation string
param parEnvironment string
param parLocAbbrev string
param parSpokeName string
param parHubSubId string
param parCompanyPrefix string
param parSpokeSubId string
param parHubNetworkResourceGroupName string
param parSpokeNetworkAddressOctets string
param parSaTags object = {}
param parContainerList array

var varEnvironments = parEnvironment == 'dev' ? ['tst', 'dev'] : ['prd', 'acc']

module storageAccounts './modules/storageAccount.bicep' = [for env in varEnvironments: {
  name: 'sa-${env}'
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parCompanyPrefix: parCompanyPrefix
    parSpokeName: parSpokeName
    parEnv: env
    parTagsSa: parSaTags
    parSpokeSubId: parSpokeSubId
    parHubSubId: parHubSubId
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parSpokeNetworkAddressOctets: parSpokeNetworkAddressOctets
    parContainerList: parContainerList
  }
}]
