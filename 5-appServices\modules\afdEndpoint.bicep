metadata name = 'ALZ Bicep - PCP Application Infrastructure - App Services'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure App Services'

param parAppName string
param parEnvironment string
param parCompanyPrefix string

var varCompanyPrefix = parCompanyPrefix == 'ttg' ? 'tcar' : parCompanyPrefix

@sys.description('Get the existing front door profile.')
resource resFrontDoorProfile 'Microsoft.Cdn/profiles@2025-06-01' existing = {
  name: 'afd-${varCompanyPrefix}-profile'
}

@sys.description('Create endpoint in Azure Front Door for this application')
resource resFrontDoorEndpoint 'Microsoft.Cdn/profiles/afdEndpoints@2025-06-01' = {
  name: 'afdep-${parAppName}${(parEnvironment == 'prd2' ? '-prd2' : '')}'
  parent: resFrontDoorProfile
  location: 'global'
  properties: {
    enabledState: 'Enabled'
  }
}

output parFrontDoorEndpointName string = resFrontDoorEndpoint.name
output parFrontDoorProfileName string = resFrontDoorProfile.name
