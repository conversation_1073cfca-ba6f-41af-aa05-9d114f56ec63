# Introduction 
TODO: Bicep project to deploy IaC for Private Client Portal - Trident Trust. 
1. Key Vault
2. Storage Accounts
3. SQL databases
4. App Services
5. Setting SQL permissions for the App Services on the databases

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

TODO: 
AppServices.bicep - varAlerts set action type when OPS team READY
PRD.Parameters file - API alerts set to OPS when OPS team ready. 