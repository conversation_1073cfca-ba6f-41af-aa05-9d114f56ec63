metadata name = 'ALZ Bicep - PCP Application Infrastructure - Storage Accounts'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure Storage Accounts'

param parLocation string
param parLocAbbrev string
param parSpokeName string
param parCompanyPrefix string
param parSpokeSubId string
param parStorageAccountName string
param parContainerList array

resource resAppStorageAcct 'Microsoft.Storage/storageAccounts@2025-01-01' existing = {
  name: parStorageAccountName
  scope: resourceGroup(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-app-${parLocAbbrev}')
}

var varResourceType = 'Microsoft.Storage/storageAccounts'
var varDataSourceType = 'Microsoft.Storage/storageAccounts/blobServices'

resource resBackupVault 'Microsoft.DataProtection/backupVaults@2025-07-01' existing = {
  name: 'bvault-${parSpokeName}-${parLocAbbrev}'
}

resource resBackupVaultPolicy 'Microsoft.DataProtection/backupVaults/backupPolicies@2025-07-01' existing = {
  parent: resBackupVault
  name: 'bkpol-blob-${parSpokeName}-${parLocAbbrev}'
}

resource resBackupInstance 'Microsoft.DataProtection/backupVaults/backupInstances@2025-01-01' = {
  parent: resBackupVault
  name: 'parStorageAccountName-${guid(resAppStorageAcct.id)}'
  properties: {
    objectType: 'BackupInstance'
    friendlyName: parStorageAccountName
    dataSourceInfo: {
      objectType: 'Datasource'
      resourceID: resAppStorageAcct.id
      resourceName: parStorageAccountName
      resourceType: varResourceType
      resourceUri: resAppStorageAcct.id
      resourceLocation: parLocation
      datasourceType: varDataSourceType
    }
    dataSourceSetInfo: {
      objectType: 'DatasourceSet'
      resourceID: resAppStorageAcct.id
      resourceName: parStorageAccountName
      resourceType: varResourceType
      resourceUri: resAppStorageAcct.id
      resourceLocation: parLocation
      datasourceType: varDataSourceType
    }
    policyInfo: {
      policyId: resBackupVaultPolicy.id
      policyParameters: {
        backupDatasourceParametersList: [
          {
            objectType: 'BlobBackupDatasourceParameters'
            containersList: parContainerList
          }
        ]
      }
    }
  }
}
