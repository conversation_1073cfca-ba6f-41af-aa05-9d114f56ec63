metadata name = 'ALZ Bicep - PCP Application Infrastructure - App Services'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure App Services'

type lockType = {
  @sys.description('Optional. Specify the name of lock.')
  name: string?

  @sys.description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @sys.description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType
param parEnvironment string
param parAppName string
param parFrontDoorProfileName string
param parFrontDoorEndpointName string
param parAppType string
param parSlotName string
param parAppHostName string
param parAppDeployHostName bool
param parAppDefaultHostName string
@sys.description('If you are using Private Link to connect to the origin, this should specify the resource ID of the Private Link resource (e.g. an App Service application, Azure Storage account, etc). If you are not using Private Link then this should be empty.')
param parPrivateEndpointResourceId string
@sys.description('If you are using Private Link to connect to the origin, this should specify the resource type of the Private Link resource. The allowed value will depend on the specific Private Link resource type you are using. If you are not using Private Link then this should be empty.')
param parPrivateLinkResourceType string
@sys.description('If you are using Private Link to connect to the origin, this should specify the location of the Private Link resource. If you are not using Private Link then this should be empty.')
param parPrivateEndpointLocation string
@sys.description('The mode that the WAF should be deployed using. In \'Prevention\' mode, the WAF will block requests it detects as malicious. In \'Detection\' mode, the WAF will not block requests and will simply log the request.')
@allowed([
  'Detection'
  'Prevention'
])
param parWafMode string = 'Prevention'
@sys.description('The Microsoft-managed WAF rule sets require the premium SKU of Front Door.')
param parWafPolicySkuName string = 'Premium_AzureFrontDoor'
@sys.description('The list of managed rule sets to configure on the WAF.')
param parWafManagedRuleSets array = []
param parWafCustomRules array = []

param parWafPolicyLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure App Services.'
}

@sys.description('When connecting to Private Link origins, we need to assemble the privateLinkOriginDetails object with various pieces of data.')
var varPrivateLinkOriginDetails = {
  privateLink: {
    id: parPrivateEndpointResourceId
  }
  groupId: (parPrivateLinkResourceType != '') ? parPrivateLinkResourceType : null
  privateLinkLocation: parPrivateEndpointLocation
  requestMessage: 'Please approve this connection from Front Door.'
}

@sys.description('Get the existing front door profile.')
resource resFrontDoorProfile 'Microsoft.Cdn/profiles@2025-06-01' existing = {
  name: parFrontDoorProfileName
}

@sys.description('get the created endpoint in Azure Front Door for this application')
resource resFrontDoorEndpoint 'Microsoft.Cdn/profiles/afdEndpoints@2025-06-01' existing = {
  name: parFrontDoorEndpointName
  parent: resFrontDoorProfile
}

@sys.description('Create a custom domain for the App Service route in the Azure Front Door.')
resource resCustomDomain 'Microsoft.Cdn/profiles/customDomains@2025-06-01' = if(parAppDeployHostName) {
  name: 'afddom-${parAppName}-${parAppType}-slot-${parSlotName}-new${(parEnvironment == 'prd2' ? '-prd2' : '')}'
  parent: resFrontDoorProfile
  properties: {
    hostName: parAppHostName
    tlsSettings: {
      certificateType: 'ManagedCertificate'
      minimumTlsVersion:'TLS12'
    }
  }
}

@sys.description('Create an origin group for the App Service route in the Azure Front Door.')
resource resFrontDoorOriginGroup 'Microsoft.Cdn/profiles/originGroups@2025-06-01' = {
  name: 'afdorigingrp-${parAppName}-${parAppType}-slot-${parSlotName}${(parEnvironment == 'prd2' ? '-prd2' : '')}'
  parent: resFrontDoorProfile
  properties: {
    loadBalancingSettings: {
      sampleSize: 4
      successfulSamplesRequired: 3
    }
    healthProbeSettings: {
      probePath: '/'
      probeRequestType: 'HEAD'
      probeProtocol: 'Https'
      probeIntervalInSeconds: 100
    }
  }
}

@sys.description('Create an origin in the origin group for the app service.')
resource resFrontDoorOrigin 'Microsoft.Cdn/profiles/originGroups/origins@2025-06-01' = {
  name: 'afdorigin-${parAppName}-${parAppType}-slot-${parSlotName}${(parEnvironment == 'prd2' ? '-prd2' : '')}'
  parent: resFrontDoorOriginGroup
  properties: {
    hostName: parAppDefaultHostName
    httpPort: 80
    httpsPort: 443
    originHostHeader: parAppDefaultHostName
    priority: 1
    weight: 1000
    sharedPrivateLinkResource: empty(parPrivateEndpointResourceId) ? null : varPrivateLinkOriginDetails
  }
}

@sys.description('Create a route for the app service in the Azure Front Door.')
resource resFrontDoorRouteCustomDomain 'Microsoft.Cdn/profiles/afdEndpoints/routes@2025-06-01' = if(parAppDeployHostName) {
  name: 'afdroute-${parAppName}-${parAppType}-slot-${parSlotName}${(parEnvironment == 'prd2' ? '-prd2' : '')}'
  parent: resFrontDoorEndpoint
  dependsOn: [
    resFrontDoorOrigin // This explicit dependency is required to ensure that the origin group is not empty when the route is created.
  ]
  properties: {
    customDomains: [
      {
        id: resCustomDomain.id
      }
    ]
    originGroup: {
      id: resFrontDoorOriginGroup.id
    }
    supportedProtocols: [
      'Https'
      'Http'
    ]
    patternsToMatch: [
      '/*'
    ]
    forwardingProtocol: 'HttpsOnly'
    linkToDefaultDomain: 'Disabled'
    httpsRedirect: 'Enabled'
  }
}

@sys.description('Create a WAF policy for the app service in the Azure Front Door.')
resource resWafPolicy 'Microsoft.Network/FrontDoorWebApplicationFirewallPolicies@2025-03-01' = {
  name: 'waf${parAppName}${parAppType}slot${parSlotName}${(parEnvironment == 'prd2' ? 'prd2' : '')}'
  location: 'global'
  sku: {
    name: parWafPolicySkuName
  }
  properties: {
    policySettings: {
      enabledState: 'Enabled'
      mode: parWafMode
    }
    managedRules: {
      managedRuleSets: parWafManagedRuleSets
    }
    customRules: {
      rules: parWafCustomRules
    }
  }
}

@sys.description('Create a resource lock for the Deployment Scripts Storage Account if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resWafPolicyLock 'Microsoft.Authorization/locks@2020-05-01' = if (parWafPolicyLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resWafPolicy
  name: parWafPolicyLock.?name ?? '${resWafPolicy.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parWafPolicyLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parWafPolicyLock.?notes
  }
}

@sys.description('Create a security policy for the app service in the Azure Front Door.')
resource securityPolicy 'Microsoft.Cdn/profiles/securityPolicies@2025-06-01' = {
  parent: resFrontDoorProfile
  name: 'secpol${parAppName}${parAppType}slot${parSlotName}${(parEnvironment == 'prd2' ? 'prd2' : '')}'
  properties: {
    parameters: {
      type: 'WebApplicationFirewall'
      wafPolicy: {
        id: resWafPolicy.id
      }
      associations: [
        {
          domains: [
            {
              id: resCustomDomain.id
            }
          ]
          patternsToMatch: [
            '/*'
          ]
        }
      ]
    }
  }
}
