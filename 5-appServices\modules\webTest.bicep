@description('Name of the webtest')
param parName string
@description('Name of the application insights resource')
param parAppInsightsName string
@description('Location for the webtest')
param parLocation string
@description('The URL to test')
param parRequestUrl string
@description('Display name for the test')
param parDisplayName string = parName
@description('Test frequency in seconds')
param parFrequency int = 300
@description('Test timeout in seconds')
param parTimeout int = 120
@description('Whether the test is enabled')
param parEnabled bool = true
@description('Whether retry is enabled')
param parRetryEnabled bool = true
@description('SSL certificate remaining lifetime check in days')
param parSSLCertRemainingLifetimeCheck int = 7
@description('Expected HTTP status code')
param parExpectedHttpStatusCode int = 200
@description('Whether to ignore HTTP status code')
param parIgnoreHttpStatusCode bool = false
@description('Whether to check SSL')
param parSSLCheck bool = true
@description('Test locations')
param parLocations array = [
  {
    Id: 'apac-sg-sin-azr'
  }
  {
    Id: 'emea-se-sto-edge'
  }
  {
    Id: 'latam-br-gru-edge'
  }
  {
    Id: 'us-tx-sn1-azr'
  }
  {
    Id: 'emea-fr-pra-edge'
  }
]

@description('Current subscription ID, used for linking the webtest to application insights.')
var varSubscriptionId = subscription().subscriptionId
@description('Current resource group name, used for linking the webtest to application insights.')
var varResourceGroupName = resourceGroup().name

resource resWebTest 'Microsoft.Insights/webtests@2022-06-15' = {
  name: parName
  location: parLocation
  tags: {
    'hidden-link:/subscriptions/${varSubscriptionId}/resourceGroups/${varResourceGroupName}/providers/microsoft.insights/components/${parAppInsightsName}': 'Resource'
  }
  properties: {
    SyntheticMonitorId: parName
    Name: parDisplayName
    Enabled: parEnabled
    Frequency: parFrequency
    Timeout: parTimeout
    Kind: 'standard'
    RetryEnabled: parRetryEnabled
    Locations: parLocations
    Request: {
      RequestUrl: parRequestUrl
      HttpVerb: 'GET'
      ParseDependentRequests: true
    }
    ValidationRules: {
      ExpectedHttpStatusCode: parExpectedHttpStatusCode
      IgnoreHttpStatusCode: parIgnoreHttpStatusCode
      SSLCheck: parSSLCheck
      SSLCertRemainingLifetimeCheck: parSSLCertRemainingLifetimeCheck
    }
  }
}

@description('Resource ID of the created webtest, used for linking to alert rules.')
output webTestId string = resWebTest.id
