metadata name = 'ALZ Bicep - PCP Landing Zone - Spoke resource groups'
metadata description = 'ALZ Bicep Module used to set up PCP Landing Zone Spoke resource groups'

targetScope = 'subscription'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parLocation string
param parResourceGroupName string
// param parResourceLockconf object = {}
// param parResourceLockConfig lockType = {
//   kind: parResourceLockconf.kind
//   notes: parResourceLockconf.notes
// }
param parTags object = {}

resource resResourceGroup 'Microsoft.Resources/resourceGroups@2024-03-01' = {
  location: parLocation
  name: parResourceGroupName
  tags: parTags
}

// @description('Create a resource lock at the resource group level')
// module modResourceGroupLock 'resourceGroupLock.bicep' = if (!empty(parResourceLockConfig ?? {}) && parResourceLockConfig.?kind != 'None') {
//   scope: resourceGroup(resResourceGroup.name)
//   name: 'deploy-${resResourceGroup.name}-lock'
//   params: {
//     parResourceGroupName: resResourceGroup.name
//     parResourceLockConfig: parResourceLockConfig
//   }
// }

output outResourceGroupName string = resResourceGroup.name
output outResourceGroupId string = resResourceGroup.id
