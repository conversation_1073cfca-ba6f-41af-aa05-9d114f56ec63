metadata name = 'ALZ Bicep - PCP Application Infrastructure - Azure Data Factory'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure Azure Data Factory'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType
param parAppName string
param parLocation string
param parLocAbbrev string
param parEnvironment string
@description('Subscription ID of the Hub.')
param parHubSubId string
@description('Prefix of company for naming resources')
param parCompanyPrefix string

param parActionGroupDevId string
param parActionGroupOpsId string
param parActionGroupTypeAcc string
param parActionGroupTypePrd string

param parAdfTags object = {}
param parSpokeSubId string
param parSqlMiServerAcc string
param parSqlMiDatabaseAcc string
param parSqlMiUserNameAcc string
param parSqlMiServerPrd string
param parSqlMiDatabasePrd string
param parSqlMiUserNamePrd string

param parHubLogAnalyticsName string
param parHubManagementResourceGroupName string

param parAzureDataFactoryLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure Azure Data Factory.'
}

@description('Set variables for the Key Vault Secrets Officer role definition')
var varKeyVaultSecretsOfficerRoleDefinitionId = resourceId(parSpokeSubId, 'Microsoft.Authorization/roleDefinitions', 'b86a8fe4-44ce-4948-aee5-eccb2c155cd7')

resource resSQLServer 'Microsoft.Sql/servers@2024-11-01-preview' existing = {
  name: 'sqlsrv-${parAppName}-${parEnvironment}-${parLocAbbrev}'
}

resource resKeyVault 'Microsoft.KeyVault/vaults@2024-12-01-preview' existing = {
  name: 'kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev}'
}

@description('Call the existing Log Analytics workspace')
resource resLogAnalyticsWorkspace 'Microsoft.OperationalInsights/workspaces@2023-09-01' existing = {
  name: parHubLogAnalyticsName
  scope: resourceGroup(parHubSubId, parHubManagementResourceGroupName)
}

resource resAzureDataFactory 'Microsoft.DataFactory/factories@2018-06-01' = {
  name: 'adf-${parAppName}-${parEnvironment}-${parLocAbbrev}'
  location: parLocation
  tags: union(parAdfTags, {
    workloadtype: 'datafactory'
    environment: parEnvironment
  })
  identity: {
    type: 'SystemAssigned'
  }
}

@sys.description('Create the diagnostic settings for the Azure Data Factory')
resource resAzureDataFactoryDiags 'Microsoft.Insights/diagnosticSettings@2021-05-01-preview' = {
  name: 'diag-${resAzureDataFactory.name}'
  scope: resAzureDataFactory
  properties: {
    logs: [
      {
        category: 'PipelineRuns'
        enabled: true
      }
      {
        category: 'TriggerRuns'
        enabled: true
      }
      {
        category: 'ActivityRuns'
        enabled: true
      }
      {
        category: 'SandboxPipelineRuns'
        enabled: true
      }
      {
        category: 'SandboxActivityRuns'
        enabled: true
      }
      {
        category: 'SSISPackageEventMessages'
        enabled: true
      }
      {
        category: 'SSISPackageExecutableStatistics'
        enabled: true
      }
      {
        category: 'SSISPackageEventMessageContext'
        enabled: true
      }
      {
        category: 'SSISPackageExecutionComponentPhases'
        enabled: true
      }
      {
        category: 'SSISPackageExecutionDataStatistics'
        enabled: true
      }
      {
        category: 'SSISIntegrationRuntimeLogs'
        enabled: true
      }
      {
        category: 'AirflowTaskLogs'
        enabled: true
      }
      {
        category: 'AirflowWorkerLogs'
        enabled: true
      }
      {
        category: 'AirflowDagProcessingLogs'
        enabled: true
      }
      {
        category: 'AirflowSchedulerLogs'
        enabled: true
      }
      {
        category: 'AirflowWebLogs'
        enabled: true
      }
    ]
    metrics: [
      {
        category: 'AllMetrics'
        enabled: true
      }
    ]
    workspaceId: resLogAnalyticsWorkspace.id
    logAnalyticsDestinationType: null
  }
}

@description('Create a resource lock for the Key Vault if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAzureDataFactoryLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAzureDataFactoryLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resAzureDataFactory
  name: parAzureDataFactoryLock.?name ?? '${resAzureDataFactory.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAzureDataFactoryLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAzureDataFactoryLock.?notes
  }
}

module modAlerts './modules/azureDataFactoryAlerts.bicep' = {
  name: 'alerts-${parAppName}'
  params: {
    parLocAbbrev: parLocAbbrev
    parAppName: parAppName
    parEnvironment: parEnvironment
    parActionGroupDevId: parActionGroupDevId
    parActionGroupOpsId: parActionGroupOpsId    
    parActionGroupTypeAcc: parActionGroupTypeAcc
    parActionGroupTypePrd: parActionGroupTypePrd
    parLocation: parLocation
  }
}

@sys.description('Assign the Azure data factory MI PrincipalId to the Key Vault as Secret Officer')
resource resRoleAssignmentKeyvaultPrd 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(resourceGroup().id, resAzureDataFactory.id, varKeyVaultSecretsOfficerRoleDefinitionId)
  scope: resKeyVault
  properties: {
    roleDefinitionId: varKeyVaultSecretsOfficerRoleDefinitionId
    principalId: resAzureDataFactory.identity.principalId
    principalType: 'ServicePrincipal'
  }
}

resource resManagedVnet 'Microsoft.DataFactory/factories/managedVirtualNetworks@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'default'
  properties: {}
}

resource resIntegrationRuntime 'Microsoft.DataFactory/factories/integrationRuntimes@2018-06-01' = {
  name: '${take(resAzureDataFactory.name, 16)}-managedVnetIr' 
  parent: resAzureDataFactory
  properties: {
    description: 'Managed VNet IR'
    type: 'Managed'
    managedVirtualNetwork: {
      referenceName: resManagedVnet.name
      type: 'ManagedVirtualNetworkReference'
    }
    typeProperties: {
      computeProperties: {
        location: 'AutoResolve'
        dataFlowProperties: {
          computeType: 'General'
          coreCount: 4
          timeToLive: 0
        }
      }
    }
  }
} 

resource managedPrivateEndpointAzureSqlDb 'Microsoft.DataFactory/factories/managedVirtualNetworks/managedPrivateEndpoints@2018-06-01' = {
  parent: resManagedVnet
  name: 'pep-adf-${resSQLServer.name}'
  properties: {
    privateLinkResourceId: resSQLServer.id
    groupId: 'sqlServer'
  }
}

resource managedPrivateEndpointAzureKeyVault 'Microsoft.DataFactory/factories/managedVirtualNetworks/managedPrivateEndpoints@2018-06-01' = {
  parent: resManagedVnet
  name: 'pep-adf-${resKeyVault.name}'
  properties: {
    privateLinkResourceId: resKeyVault.id
    groupId: 'vault'
  }
}

resource resAzureSQLPrdTargetLinkedService 'Microsoft.DataFactory/factories/linkedservices@2018-06-01' = {
  name: 'AzureSQLPrdTargetLinkedService'
  parent: resAzureDataFactory
  properties: {
    connectVia: {
      referenceName: resIntegrationRuntime.name
      type: 'IntegrationRuntimeReference'
    }
    description: 'Connection to target Azure SQL Database'
    type: 'AzureSqlDatabase'
    typeProperties: {
      server: resSQLServer.properties.fullyQualifiedDomainName
      database: 'sqldb-${parAppName}-${parEnvironment == 'dev' ? 'tst' : 'prd'}'
      authenticationType: 'SystemAssignedManagedIdentity'
      trustServerCertificate: 'true'
      encrypt: 'true'
      connectTimeout: 100
      commandTimeout: 100
    }
  }
}

resource resAzureSQLAccTargetLinkedService 'Microsoft.DataFactory/factories/linkedservices@2018-06-01' = {
  name: 'AzureSQLAccTargetLinkedService'
  parent: resAzureDataFactory
  properties: {
    connectVia: {
      referenceName: resIntegrationRuntime.name
      type: 'IntegrationRuntimeReference'
    }
    description: 'Connection to target Azure SQL Database'
    type: 'AzureSqlDatabase'
    typeProperties: {
      server: resSQLServer.properties.fullyQualifiedDomainName
      database: 'sqldb-${parAppName}-${parEnvironment == 'dev' ? 'dev' : 'acc'}'
      authenticationType: 'SystemAssignedManagedIdentity'
      trustServerCertificate: 'true'
      encrypt: 'true'
      connectTimeout: 100
      commandTimeout: 100
    }
  }
}

resource resAzureKeyVaultLinkedService 'Microsoft.DataFactory/factories/linkedservices@2018-06-01' = {
  name: 'AzureKVLinkedService'
  parent: resAzureDataFactory
  properties: {
    connectVia: {
      referenceName: resIntegrationRuntime.name
      type: 'IntegrationRuntimeReference'
    }
    description: 'Connection to Azure Key Vault'
    type: 'AzureKeyVault'
    typeProperties: {
      baseUrl: resKeyVault.properties.vaultUri
    }
  }
}

resource resAzureSqlManagedInstancePrdTargetLinkedService 'Microsoft.DataFactory/factories/linkedservices@2018-06-01' = {
  name: 'AzureSQLMIPrdTargetLinkedService'
  parent: resAzureDataFactory
  properties: {
    connectVia: {
      referenceName: resIntegrationRuntime.name
      type: 'IntegrationRuntimeReference'
    }
    description: 'Connection to target Azure SQL Managed Instance for PRD'
    type: 'AzureSqlMI'
    typeProperties: {
      server: parSqlMiServerPrd
      database: parSqlMiDatabasePrd
      encrypt: 'mandatory'
      trustServerCertificate: false
      authenticationType: 'SQL'
      userName: parSqlMiUserNamePrd
      password: {
        secretName: 'secret-vp-sqlmi-db-password-Prd'
        type: 'AzureKeyVaultSecret'
        store: {
          referenceName: resAzureKeyVaultLinkedService.name
          type: 'LinkedServiceReference'
        }
      }
      connectTimeout: 100
      commandTimeout: 100
    }
  }
}

resource resAzureSqlManagedInstanceAccTargetLinkedService 'Microsoft.DataFactory/factories/linkedservices@2018-06-01' = {
  name: 'AzureSQLMITargetLinkedService'
  parent: resAzureDataFactory
  properties: {
    connectVia: {
      referenceName: resIntegrationRuntime.name
      type: 'IntegrationRuntimeReference'
    }
    description: 'Connection to target Azure SQL Managed Instance for ACC'
    type: 'AzureSqlMI'
    typeProperties: {
      server: parSqlMiServerAcc
      database: parSqlMiDatabaseAcc
      encrypt: 'mandatory'
      trustServerCertificate: false
      authenticationType: 'SQL'
      userName: parSqlMiUserNameAcc
      password: {
        secretName: 'secret-vp-sqlmi-db-password-Acc'
        type: 'AzureKeyVaultSecret'
        store: {
          referenceName: resAzureKeyVaultLinkedService.name
          type: 'LinkedServiceReference'
        }
      }
      connectTimeout: 100
      commandTimeout: 100
    }
  }
}

resource resDataSet_SQL_Azuredb_prd 'Microsoft.DataFactory/factories/datasets@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'SQL_Azuredb_prd'
  properties: {
    linkedServiceName: {
      referenceName: 'AzureSQLPrdTargetLinkedService'
      type: 'LinkedServiceReference'
    }
    parameters: {
      Tablename: {
        type: 'string'
      }
    }
    folder: {
      name: 'SQL_AZURE_DATASETS_PRD'
    }
    annotations: []
    type: 'AzureSqlTable'
    schema: []
    typeProperties: {
      table: {
        value: '@dataset().Tablename'
        type: 'Expression'
      }
    }
  }
  dependsOn: [
    resAzureSQLPrdTargetLinkedService
  ]
}

resource resDataSet_SQL_Azuredb_acc 'Microsoft.DataFactory/factories/datasets@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'SQL_Azuredb_acc'
  properties: {
    linkedServiceName: {
      referenceName: 'AzureSQLAccTargetLinkedService'
      type: 'LinkedServiceReference'
    }
    parameters: {
      Tablename: {
        type: 'string'
      }
    }
    folder: {
      name: 'SQL_AZURE_DATASETS_ACC'
    }
    annotations: []
    type: 'AzureSqlTable'
    schema: []
    typeProperties: {
      schema: 'dbo'
      table: {
        value: '@dataset().Tablename'
        type: 'Expression'
      }
    }
  }
  dependsOn: [
    resAzureSQLAccTargetLinkedService
  ]
}

resource resDataSet_SQL_MI_PCP_DYNAMIC 'Microsoft.DataFactory/factories/datasets@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'SQL_MI_PCP_DYNAMIC'
  properties: {
    linkedServiceName: {
      referenceName: 'AzureSQLMITargetLinkedService'
      type: 'LinkedServiceReference'
    }
    parameters: {
      Table_name_MI: {
        type: 'string'
      }
      Schemaname_MI: {
        type: 'string'
      }
    }
    folder: {
      name: 'SQL_MI_DATASETS_ACC'
    }
    annotations: []
    type: 'AzureSqlMITable'
    schema: []
    typeProperties: {
      schema: {
        value: '@dataset().Schemaname_MI'
        type: 'Expression'
      }
      table: {
        value: '@dataset().Table_name_MI'
        type: 'Expression'
      }
    }
  }
  dependsOn: [
    resAzureSqlManagedInstanceAccTargetLinkedService
  ]
}

resource resDataSet_SQL_MI_PCP_DYNAMIC_PROD 'Microsoft.DataFactory/factories/datasets@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'SQL_MI_PCP_DYNAMIC_PROD'
  properties: {
    linkedServiceName: {
      referenceName: 'AzureSQLMIPrdTargetLinkedService'
      type: 'LinkedServiceReference'
    }
    parameters: {
      Table_name_MI: {
        type: 'string'
      }
      Schemaname_MI: {
        type: 'string'
      }
    }
    folder: {
      name: 'SQL_MI_DATASETS_PRD'
    }
    annotations: []
    type: 'AzureSqlMITable'
    schema: []
    typeProperties: {
      schema: {
        value: '@dataset().Schemaname_MI'
        type: 'Expression'
      }
      table: {
        value: '@dataset().Table_name_MI'
        type: 'Expression'
      }
    }
  }
  dependsOn: [
    resAzureSqlManagedInstancePrdTargetLinkedService
  ]
}

resource resPipeline_CREATE_DYNAMIC_SP 'Microsoft.DataFactory/factories/pipelines@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'CREATE DYNAMIC SP'
  properties: {
    activities: [
      {
        name: 'Create SP'
        type: 'Script'
        state: 'Inactive'
        onInactiveMarkAs: 'Succeeded'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        linkedServiceName: {
          referenceName: 'AzureSQLAccTargetLinkedService'
          type: 'LinkedServiceReference'
        }
        typeProperties: {
          scripts: [
            {
              type: 'Query'
              text: 'EXEC sp_rename \'Staging_pcp_shareholders\', \'Staging_pcp_shareholders_TEMP\';'
            }
          ]
          scriptBlockExecutionTimeout: '02:00:00'
        }
      }
      {
        name: 'Lookup1'
        type: 'Lookup'
        state: 'Inactive'
        onInactiveMarkAs: 'Succeeded'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'AzureSqlSource'
            sqlReaderQuery: 'SELECT name\nFROM sys.tables\nWHERE name LIKE \'%Staging_PCP%\';'
            queryTimeout: '02:00:00'
            partitionOption: 'None'
          }
          dataset: {
            referenceName: 'SQL_Azuredb_acc'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'x'
            }
          }
          firstRowOnly: false
        }
      }
      {
        name: 'Lookup1_copy1'
        type: 'Lookup'
        state: 'Inactive'
        onInactiveMarkAs: 'Succeeded'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'AzureSqlSource'
            sqlReaderQuery: 'EXEC sp_rename \'Staging_pcp_shareholders_TEMP\', \'Staging_pcp_shareholders\';'
            queryTimeout: '02:00:00'
            partitionOption: 'None'
          }
          dataset: {
            referenceName: 'SQL_Azuredb_acc'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'x'
            }
          }
          firstRowOnly: false
        }
      }
      {
        name: 'Create SP_copy1'
        type: 'Script'
        state: 'Inactive'
        onInactiveMarkAs: 'Succeeded'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        linkedServiceName: {
          referenceName: 'AzureSQLPrdTargetLinkedService'
          type: 'LinkedServiceReference'
        }
        typeProperties: {
          scripts: [
            {
              type: 'Query'
              text: 'CREATE PROCEDURE [dbo].[sp_getColumnMapping]\n  @schema_name VARCHAR(100),\n  @table_name VARCHAR(100) \nAS\nBEGIN\n  SET NOCOUNT ON;\n\n  DECLARE @json_construct NVARCHAR(MAX) = N\'{"type": "TabularTranslator", "mappings": {X}}\';\n  DECLARE @json NVARCHAR(MAX);\n\n  -- Bouw de JSON op met de kolomnamen uit de TableColumnMapping tabel\n  SET @json = (\n      SELECT \n          (SELECT ColumnName AS \'name\' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER) AS source,\n          (SELECT ColumnName AS \'name\' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER) AS target\n      FROM [dbo].[TableColumnMapping]\n      WHERE TableName = @table_name\n     \n      FOR JSON PATH\n  );\n\n  -- Voorkom NULL waarden\n  SET @json = ISNULL(@json, \'[]\');\n\n  -- JSON-output correct formatteren\n  SELECT REPLACE(@json_construct, \'{X}\', @json) AS json_output;\nEND;\n\n\n\n'
            }
          ]
          scriptBlockExecutionTimeout: '02:00:00'
        }
      }
    ]
    policy: {
      elapsedTimeMetric: {}
    }
    annotations: []
  }
  dependsOn: [
    resAzureSQLAccTargetLinkedService
    resDataSet_SQL_Azuredb_acc
    resAzureSQLPrdTargetLinkedService
  ]
}

resource resPipeline_PCP_ACC_DYN_PIPELINE 'Microsoft.DataFactory/factories/pipelines@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'PCP_ACC_DYN_PIPELINE'
  properties: {
    activities: [
      {
        name: 'pcp_Data_Stream'
        type: 'Copy'
        dependsOn: [
          {
            activity: 'Lookup Mapping structure'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'SqlMISource'
            partitionOption: 'None'
          }
          sink: {
            type: 'AzureSqlSink'
            writeBehavior: 'insert'
            sqlWriterUseTableLock: false
            tableOption: 'autoCreate'
            disableMetricsCollection: false
          }
          enableStaging: false
          translator: {
            value: '@activity(\'Lookup Mapping structure\').output.firstRow'
            type: 'Expression'
          }
        }
        inputs: [
          {
            referenceName: 'SQL_MI_PCP_DYNAMIC'
            type: 'DatasetReference'
            parameters: {
              Table_name_MI: '@pipeline().parameters.Table_name_src'
              Schemaname_MI: 'dbo'
            }
          }
        ]
        outputs: [
          {
            referenceName: 'SQL_Azuredb_acc'
            type: 'DatasetReference'
            parameters: {
              Tablename: {
                value: '@concat(\'Staging_\', pipeline().parameters.Table_Name_src, \'_temp\')\n'
                type: 'Expression'
              }
            }
          }
        ]
      }
      {
        name: 'Truncate sink table if exist'
        type: 'Script'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        linkedServiceName: {
          referenceName: 'AzureSQLAccTargetLinkedService'
          type: 'LinkedServiceReference'
        }
        typeProperties: {
          scripts: [
            {
              type: 'Query'
              text: {
                value: '@concat(\'DROP TABLE IF EXISTS \', pipeline().parameters.Table_Name)\n\n'
                type: 'Expression'
              }
            }
          ]
          scriptBlockExecutionTimeout: '02:00:00'
        }
      }
      {
        name: 'Rename _temp'
        type: 'Script'
        dependsOn: [
          {
            activity: 'pcp_Data_Stream'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        linkedServiceName: {
          referenceName: 'AzureSQLAccTargetLinkedService'
          type: 'LinkedServiceReference'
        }
        typeProperties: {
          scripts: [
            {
              type: 'Query'
              text: {
                value: 'EXEC sp_rename \'@{pipeline().parameters.Table_Name_temp}\', \'@{pipeline().parameters.Table_Name}\';\n'
                type: 'Expression'
              }
            }
          ]
          scriptBlockExecutionTimeout: '02:00:00'
        }
      }
      {
        name: 'Lookup Mapping structure'
        type: 'Lookup'
        dependsOn: [
          {
            activity: 'Truncate sink table if exist'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'AzureSqlSource'
            sqlReaderStoredProcedureName: '[dbo].[sp_getColumnMapping]'
            storedProcedureParameters: {
              schema_name: {
                type: 'String'
                value: 'dbo'
              }
              table_name: {
                type: 'String'
                value: {
                  value: '@pipeline().parameters.Table_name_src'
                  type: 'Expression'
                }
              }
            }
            queryTimeout: '02:00:00'
            partitionOption: 'None'
          }
          dataset: {
            referenceName: 'SQL_Azuredb_acc'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'TableColumnMapping'
            }
          }
          firstRowOnly: true
        }
      }
    ]
    policy: {
      elapsedTimeMetric: {}
    }
    parameters: {
      Table_Name: {
        type: 'string'
      }
      Table_Name_TEMP: {
        type: 'string'
      }
      Schema_Name: {
        type: 'string'
        defaultValue: 'dbo'
      }
      Table_name_src: {
        type: 'string'
      }
    }
    folder: {
      name: 'PCP_ACC_DYN_PIPELINE'
    }
    annotations: []
  }
  dependsOn: [
    resDataSet_SQL_MI_PCP_DYNAMIC
    resDataSet_SQL_Azuredb_acc
    resAzureSQLAccTargetLinkedService
  ]
}

resource resPipeline_PCP_PRD_DYN_PIPELINE 'Microsoft.DataFactory/factories/pipelines@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'PCP_PRD_DYN_PIPELINE'
  properties: {
    activities: [
      {
        name: 'pcp_Data_Stream'
        type: 'Copy'
        dependsOn: [
          {
            activity: 'Lookup Mapping structure'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'SqlMISource'
            partitionOption: 'None'
          }
          sink: {
            type: 'AzureSqlSink'
            writeBehavior: 'insert'
            sqlWriterUseTableLock: false
            tableOption: 'autoCreate'
            disableMetricsCollection: false
          }
          enableStaging: false
          translator: {
            value: '@activity(\'Lookup Mapping structure\').output.firstRow'
            type: 'Expression'
          }
        }
        inputs: [
          {
            referenceName: 'SQL_MI_PCP_DYNAMIC_PROD'
            type: 'DatasetReference'
            parameters: {
              Table_name_MI: {
                value: '@pipeline().parameters.Table_name_src'
                type: 'Expression'
              }
              Schemaname_MI: 'dbo'
            }
          }
        ]
        outputs: [
          {
            referenceName: 'SQL_Azuredb_prd'
            type: 'DatasetReference'
            parameters: {
              Tablename: {
                value: '@concat(\'Staging_\', pipeline().parameters.Table_Name_src, \'_TEMP\')'
                type: 'Expression'
              }
            }
          }
        ]
      }
      {
        name: 'Truncate sink table if exist'
        type: 'Script'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        linkedServiceName: {
          referenceName: 'AzureSQLPrdTargetLinkedService'
          type: 'LinkedServiceReference'
        }
        typeProperties: {
          scripts: [
            {
              type: 'Query'
              text: {
                value: '@concat(\'DROP TABLE IF EXISTS \', pipeline().parameters.Table_Name)\n\n'
                type: 'Expression'
              }
            }
          ]
          scriptBlockExecutionTimeout: '02:00:00'
        }
      }
      {
        name: 'Rename _temp'
        type: 'Script'
        dependsOn: [
          {
            activity: 'pcp_Data_Stream'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        linkedServiceName: {
          referenceName: 'AzureSQLPrdTargetLinkedService'
          type: 'LinkedServiceReference'
        }
        typeProperties: {
          scripts: [
            {
              type: 'Query'
              text: {
                value: 'EXEC sp_rename \'@{pipeline().parameters.Table_Name_TEMP}\', \'@{pipeline().parameters.Table_Name}\';\n'
                type: 'Expression'
              }
            }
          ]
          scriptBlockExecutionTimeout: '02:00:00'
        }
      }
      {
        name: 'Lookup Mapping structure'
        type: 'Lookup'
        dependsOn: [
          {
            activity: 'Truncate sink table if exist'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'AzureSqlSource'
            sqlReaderStoredProcedureName: '[dbo].[sp_getColumnMapping]'
            storedProcedureParameters: {
              schema_name: {
                type: 'String'
                value: 'dbo'
              }
              table_name: {
                type: 'String'
                value: {
                  value: '@pipeline().parameters.Table_name_src'
                  type: 'Expression'
                }
              }
            }
            queryTimeout: '02:00:00'
            partitionOption: 'None'
          }
          dataset: {
            referenceName: 'SQL_Azuredb_prd'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'TableColumnMapping'
            }
          }
          firstRowOnly: true
        }
      }
    ]
    policy: {
      elapsedTimeMetric: {}
    }
    parameters: {
      Table_Name: {
        type: 'string'
      }
      Table_Name_TEMP: {
        type: 'string'
      }
      Schema_Name: {
        type: 'string'
        defaultValue: 'dbo'
      }
      Table_name_src: {
        type: 'string'
      }
    }
    folder: {
      name: 'PCP_PRD_DYN_PIPELINE'
    }
    annotations: []
  }
  dependsOn: [
    resDataSet_SQL_MI_PCP_DYNAMIC_PROD
    resDataSet_SQL_Azuredb_prd
    resAzureSQLPrdTargetLinkedService
  ]
}

resource resPipeline_GET_PRD_Tablenames_Metadata 'Microsoft.DataFactory/factories/pipelines@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'GET_PRD_Tablenames_Metadata'
  properties: {
    activities: [
      {
        name: 'Copy data1'
        type: 'Copy'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'SqlMISource'
            sqlReaderQuery: 'SELECT \r\n    TABLE_NAME AS \'Table Name\',\r\n    COLUMN_NAME AS \'Column Name\'\r\nFROM \r\n    INFORMATION_SCHEMA.COLUMNS\r\nWHERE \r\n    TABLE_SCHEMA = \'dbo\'  -- Pas dit aan voor het gewenste schema\r\n    AND TABLE_NAME LIKE \'pcp_%\'  -- Alleen tabellen die beginnen met \'pcp_\'\r\nORDER BY \r\n    TABLE_NAME, COLUMN_NAME;\r\n'
            partitionOption: 'None'
          }
          sink: {
            type: 'AzureSqlSink'
            preCopyScript: 'TRUNCATE TABLE [dbo].[TableColumnMapping]\n   '
            writeBehavior: 'insert'
            sqlWriterUseTableLock: false
            tableOption: 'autoCreate'
            disableMetricsCollection: false
          }
          enableStaging: false
          translator: {
            type: 'TabularTranslator'
            mappings: [
              {
                source: {
                  name: 'Table Name'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
                sink: {
                  name: 'TableName'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
              }
              {
                source: {
                  name: 'Column Name'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
                sink: {
                  name: 'ColumnName'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
              }
            ]
            typeConversion: true
            typeConversionSettings: {
              allowDataTruncation: true
              treatBooleanAsNumber: false
            }
          }
        }
        inputs: [
          {
            referenceName: 'SQL_MI_PCP_DYNAMIC'
            type: 'DatasetReference'
            parameters: {
              Table_name_MI: 'x'
              Schemaname_MI: 'dbo'
            }
          }
        ]
        outputs: [
          {
            referenceName: 'SQL_Azuredb_prd'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'TableColumnMapping'
            }
          }
        ]
      }
      {
        name: 'Lookup1'
        type: 'Lookup'
        dependsOn: [
          {
            activity: 'Copy data1'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'AzureSqlSource'
            sqlReaderQuery: 'SELECT distinct [TableName]\n      \n  FROM [dbo].[TableColumnMapping]'
            queryTimeout: '02:00:00'
            partitionOption: 'None'
          }
          dataset: {
            referenceName: 'SQL_Azuredb_prd'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'TableColumnMapping'
            }
          }
          firstRowOnly: false
        }
      }
      {
        name: 'pipeline1'
        type: 'ForEach'
        dependsOn: [
          {
            activity: 'Lookup1'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        userProperties: []
        typeProperties: {
          items: {
            value: '@activity(\'Lookup1\').output.value'
            type: 'Expression'
          }
          isSequential: false
          activities: [
            {
              name: 'Execute PipelineDEV'
              type: 'ExecutePipeline'
              dependsOn: []
              policy: {
                secureInput: false
              }
              userProperties: []
              typeProperties: {
                pipeline: {
                  referenceName: 'PCP_PRD_DYN_PIPELINE'
                  type: 'PipelineReference'
                }
                waitOnCompletion: true
                parameters: {
                  Table_Name: {
                    value: '@concat(\'Staging_\', item().TableName)'
                    type: 'Expression'
                  }
                  Table_Name_TEMP: {
                    value: '@concat(\'Staging_\', item().TableName, \'_TEMP\')\n'
                    type: 'Expression'
                  }
                  Table_name_src: {
                    value: '@item().Tablename'
                    type: 'Expression'
                  }
                }
              }
            }
          ]
        }
      }
    ]
    policy: {
      elapsedTimeMetric: {}
    }
    folder: {
      name: 'PCP_PRD_DYN_PIPELINE'
    }
    annotations: []
  }
  dependsOn: [
    resDataSet_SQL_MI_PCP_DYNAMIC
    resDataSet_SQL_Azuredb_prd
    resPipeline_PCP_PRD_DYN_PIPELINE
  ]
}

resource resPipeline_GET_ACC_Tablenames_Metadata 'Microsoft.DataFactory/factories/pipelines@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'GET_ACC_Tablenames_Metadata'
  properties: {
    activities: [
      {
        name: 'Copy data1'
        type: 'Copy'
        dependsOn: []
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'SqlMISource'
            sqlReaderQuery: 'SELECT \r\n    TABLE_NAME AS \'Table Name\',\r\n    COLUMN_NAME AS \'Column Name\'\r\nFROM \r\n    INFORMATION_SCHEMA.COLUMNS\r\nWHERE \r\n    TABLE_SCHEMA = \'dbo\'  -- Pas dit aan voor het gewenste schema\r\n    AND TABLE_NAME LIKE \'pcp_%\'  -- Alleen tabellen die beginnen met \'pcp_\'\r\nORDER BY \r\n    TABLE_NAME, COLUMN_NAME;\r\n'
            partitionOption: 'None'
          }
          sink: {
            type: 'AzureSqlSink'
            preCopyScript: 'TRUNCATE TABLE [dbo].[TableColumnMapping]\n   '
            writeBehavior: 'insert'
            sqlWriterUseTableLock: false
            tableOption: 'autoCreate'
            disableMetricsCollection: false
          }
          enableStaging: false
          translator: {
            type: 'TabularTranslator'
            mappings: [
              {
                source: {
                  name: 'Table Name'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
                sink: {
                  name: 'TableName'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
              }
              {
                source: {
                  name: 'Column Name'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
                sink: {
                  name: 'ColumnName'
                  type: 'String'
                  physicalType: 'nvarchar'
                }
              }
            ]
            typeConversion: true
            typeConversionSettings: {
              allowDataTruncation: true
              treatBooleanAsNumber: false
            }
          }
        }
        inputs: [
          {
            referenceName: 'SQL_MI_PCP_DYNAMIC'
            type: 'DatasetReference'
            parameters: {
              Table_name_MI: 'x'
              Schemaname_MI: 'dbo'
            }
          }
        ]
        outputs: [
          {
            referenceName: 'SQL_Azuredb_acc'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'TableColumnMapping'
            }
          }
        ]
      }
      {
        name: 'Lookup1'
        type: 'Lookup'
        dependsOn: [
          {
            activity: 'Copy data1'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        policy: {
          timeout: '0.12:00:00'
          retry: 0
          retryIntervalInSeconds: 30
          secureOutput: false
          secureInput: false
        }
        userProperties: []
        typeProperties: {
          source: {
            type: 'AzureSqlSource'
            sqlReaderQuery: 'SELECT distinct [TableName]\n      \n  FROM [dbo].[TableColumnMapping]'
            queryTimeout: '02:00:00'
            partitionOption: 'None'
          }
          dataset: {
            referenceName: 'SQL_Azuredb_acc'
            type: 'DatasetReference'
            parameters: {
              Tablename: 'TableColumnMapping'
            }
          }
          firstRowOnly: false
        }
      }
      {
        name: 'ForEach1'
        type: 'ForEach'
        dependsOn: [
          {
            activity: 'Lookup1'
            dependencyConditions: [
              'Succeeded'
            ]
          }
        ]
        userProperties: []
        typeProperties: {
          items: {
            value: '@activity(\'Lookup1\').output.value'
            type: 'Expression'
          }
          isSequential: false
          activities: [
            {
              name: 'Execute PipelineDEV'
              type: 'ExecutePipeline'
              dependsOn: []
              policy: {
                secureInput: false
              }
              userProperties: []
              typeProperties: {
                pipeline: {
                  referenceName: 'PCP_ACC_DYN_PIPELINE'
                  type: 'PipelineReference'
                }
                waitOnCompletion: true
                parameters: {
                  Table_Name: {
                    value: '@concat(\'Staging_\', item().TableName)'
                    type: 'Expression'
                  }
                  Table_Name_TEMP: {
                    value: '@concat(\'Staging_\', item().TableName, \'_TEMP\')\n'
                    type: 'Expression'
                  }
                  Table_name_src: {
                    value: '@item().Tablename'
                    type: 'Expression'
                  }
                }
              }
            }
          ]
        }
      }
    ]
    policy: {
      elapsedTimeMetric: {}
    }
    folder: {
      name: 'PCP_ACC_DYN_PIPELINE'
    }
    annotations: []
  }
  dependsOn: [
    resDataSet_SQL_MI_PCP_DYNAMIC
    resDataSet_SQL_Azuredb_acc
    resPipeline_PCP_ACC_DYN_PIPELINE
  ]
}

resource resTrigger_ACC 'Microsoft.DataFactory/factories/triggers@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'ACC_trigger'
  properties: {
    annotations: []
    pipelines: [
      {
        pipelineReference: {
          referenceName: 'GET_ACC_Tablenames_Metadata'
          type: 'PipelineReference'
        }
        parameters: {}
      }
    ]
    type: 'ScheduleTrigger'
    typeProperties: {
      recurrence: {
        frequency: 'Hour'
        interval: 1
        startTime: '2025-02-03T16:58:00'
        timeZone: 'W. Europe Standard Time'
      }
    }
  }
  dependsOn: [
    resPipeline_GET_ACC_Tablenames_Metadata
  ]
}

resource resTrigger_PRD 'Microsoft.DataFactory/factories/triggers@2018-06-01' = {
  parent: resAzureDataFactory
  name: 'PRD_Trigger'
  properties: {
    annotations: []
    pipelines: [
      {
        pipelineReference: {
          referenceName: 'GET_PRD_Tablenames_Metadata'
          type: 'PipelineReference'
        }
        parameters: {}
      }
    ]
    type: 'ScheduleTrigger'
    typeProperties: {
      recurrence: {
        frequency: 'Day'
        interval: 1
        startTime: '2025-04-15T22:45:00Z'
        timeZone: 'UTC'
        schedule: {}
      }
    }
  }
  dependsOn: [
    resPipeline_GET_PRD_Tablenames_Metadata
  ]
}

