@description('Email address to send alerts to NetPro Support')
param parNetProSupportEmail string

@description('Short name of the action group (max 12 characters)')
param parActionGroupShortName string

@description('Display name for the action group')
param parActionGroupDisplayName string

resource actionGroup 'microsoft.insights/actionGroups@2024-10-01-preview' = {
  name: parActionGroupDisplayName
  location: 'Global'
  properties: {
    groupShortName: parActionGroupShortName
    enabled: true
    emailReceivers: [
      {
        name: 'Email NetPro Support'
        emailAddress: parNetProSupportEmail
        useCommonAlertSchema: true
      }
    ]
    smsReceivers: []
    webhookReceivers: []
    eventHubReceivers: []
    itsmReceivers: []
    azureAppPushReceivers: []
    automationRunbookReceivers: []
    voiceReceivers: []
    logicAppReceivers: []
    azureFunctionReceivers: []
    armRoleReceivers: []
  }
}


output actionGroupId string = actionGroup.id
