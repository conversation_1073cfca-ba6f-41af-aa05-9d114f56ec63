metadata name = 'ALZ Bicep - PCP Application Infrastructure - App Services'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure App Services'

type lockType = {
  @sys.description('Optional. Specify the name of lock.')
  name: string?

  @sys.description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @sys.description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType

@sys.description('Main parameters')
param parLocation string
param parLocAbbrev string
param parEnvironment string
param parCompanyPrefix string
#disable-next-line no-unused-params
param parTenantId string
param parHubSubId string
param parSpokeSubId string
param parHubLogAnalyticsName string
param parHubNetworkResourceGroupName string
param parHubManagementResourceGroupName string
param parAppName string
param parCustomerDomain string
param parSpokeName string
param parAppTags object = {}

// @sys.description('parameters for location of MongoDB for migration purposes. This can be deleted after migration completed')
// param parMongoDbPepsDev array = []
// param parMongoDbPepsPrd array = []

param parActionGroupDevId string
param parActionGroupOpsId string
param parActionGroupTypeAcc string
param parActionGroupTypePrd string

param parApps array = []
param parSCMIpSecurityRestrictions array = []
#disable-next-line no-unused-params
param parMgtClientIdPrd string
#disable-next-line no-unused-params
param parMgtClientIdAcc string
#disable-next-line no-unused-params
param parCltClientIdPrd string
#disable-next-line no-unused-params
param parCltClientIdAcc string
#disable-next-line no-unused-params
param parCltClientIdExternalIdPrd string
#disable-next-line no-unused-params
param parCltClientIdExternalIdAcc string
#disable-next-line no-unused-params
param parApiClientIdPrd string
#disable-next-line no-unused-params
param parApiClientIdAcc string
#disable-next-line no-unused-params
param parExternalTenantId string
#disable-next-line no-unused-params
param parTenantSubDomainExternalId string
#disable-next-line no-unused-params
param parMongoDbConnStrPrd string
#disable-next-line no-unused-params
param parMongoDbConnStrDev string
#disable-next-line no-unused-params
param parMongoDbNamePrd string
#disable-next-line no-unused-params
param parMongoDbNameAcc string

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varSubnetIDWebSrvInt = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'webAppsOutboundSubnet')
var varSubnetIDApiSrvInt = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'apiAppsOutboundSubnet')

@sys.description('Call the existing app service plan for public and internal web apps')
resource appServicePlanWeb 'Microsoft.Web/serverfarms@2024-11-01' existing = {
  name: 'asp-linux-${parSpokeName}-web-${parLocAbbrev}'
  scope: resourceGroup('rg-${parCompanyPrefix}-${parSpokeName}-sharedservices-${parLocAbbrev}')
}
resource appServicePlanApi 'Microsoft.Web/serverfarms@2024-11-01' existing = {
  name: 'asp-linux-${parSpokeName}-api-${parLocAbbrev}'
  scope: resourceGroup('rg-${parCompanyPrefix}-${parSpokeName}-sharedservices-${parLocAbbrev}')
}

@sys.description('Call Module that creates the Azure Front Door Endpoint')
module modAfdEndpoint 'modules/afdEndpoint.bicep' = {
  name: '${parSpokeName}-afdEndpoint-${uniqueString(resourceGroup().id)}'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parCompanyPrefix: parCompanyPrefix
    parEnvironment: parEnvironment
    parAppName: parAppName
  }
}

module apps 'modules/appServices.bicep' = [for app in parApps: {
  name: app.type
  params: {
    parGlobalResourceLock: parGlobalResourceLock
    parLocation: parLocation
    parLocAbbrev: parLocAbbrev
    parEnvironment: parEnvironment
    parCompanyPrefix: parCompanyPrefix
    parHubSubId: parHubSubId
    parSpokeSubId: parSpokeSubId
    parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
    parHubManagementResourceGroupName: parHubManagementResourceGroupName
    parAppName: parAppName
    parSpokeName: parSpokeName
    parAppTags: parAppTags
    parAppType: app.type
    parServerFarmWebId: appServicePlanWeb.id  
    parServerFarmApiId: appServicePlanApi.id
    parAppHostName: app.hostname
    parCustomerDomain: parCustomerDomain
    parAppDeployHostName: app.deployhostname
    parSubnetIDWebSrvInt: varSubnetIDWebSrvInt
    parSubnetIDApiSrvInt: varSubnetIDApiSrvInt
    parSCMIpSecurityRestrictions: parSCMIpSecurityRestrictions
    parAppSettingsPrd: app.appsettingsprd
    parAppSettingsAcc: app.appsettingsacc
    parAppPrivateIpPrd: app.appPrivateIpPrd
    parAppPrivateIpAcc: app.appPrivateIpAcc
    parWafManagedRuleSetsPrd: app.wafmanagedrulesetsprd
    parWafManagedRuleSetsAcc: app.wafmanagedrulesetsacc
    parWafCustomRulesPrd: app.wafcustomrulesprd
    parWafCustomRulesAcc: app.wafcustomrulesacc
    parHealthCheckPath: app.healthCheckPath
    parEnableAvailabilityTestAcc: app.enableAvailabilityTestAcc
    parEnableAvailabilityTestPrd: app.enableAvailabilityTestPrd
    parAlerts: app.alerts
    parEnableHealthCheckAlertingAcc: app.enableHealthCheckAlertingAcc
    parEnableHealthCheckAlertingPrd: app.enableHealthCheckAlertingPrd
    parFrontDoorEndpointName: modAfdEndpoint.outputs.parFrontDoorEndpointName
    parFrontDoorProfileName: modAfdEndpoint.outputs.parFrontDoorProfileName
    parActionGroupDevId: parActionGroupDevId
    parActionGroupOpsId: parActionGroupOpsId
    parActionGroupTypeAcc: parActionGroupTypeAcc
    parActionGroupTypePrd: parActionGroupTypePrd
    parHubLogAnalyticsName: parHubLogAnalyticsName
  }
}]

// @description('deploy private endpoint to mongodb tnev. This can be deleted after migration completed. When more endpoints need to be created the info will be moved to an array and looped through')
// module modMongoDbPepDev 'modules/mongoDbPep.bicep' = [for mongoDb in parMongoDbPepsDev: if (parEnvironment == 'dev') {
//   name: 'mongoDbPepDev-${mongoDb.MongoDBAcctName}-${uniqueString(resourceGroup().id)}'
//   params: {
//     parLocation: parLocation
//     parLocAbbrev: parLocAbbrev
//     parCompanyPrefix: parCompanyPrefix
//     parEnvironment: parEnvironment
//     parMongoDBAcctName: mongoDb.MongoDBAcctName
//     parMongoSubId: mongoDb.MongoSubId
//     parMongoResourceGroupName: mongoDb.MongoResourceGroupName
//     parSpokeName: parSpokeName
//     parSpokeSubId: parSpokeSubId
//     parHubSubId: parHubSubId
//     parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
//   }
//   dependsOn: [
//     apps
//   ]
// }]

// @description('deploy private endpoint to mongodb tnev. This can be deleted after migration completed. When more endpoints need to be created the info will be moved to an array and looped through')
// module modMongoDbPepPrd 'modules/mongoDbPep.bicep' = [for mongoDb in parMongoDbPepsPrd: if (parEnvironment == 'prd') {
//   name: 'mongoDbPepPrd-${mongoDb.MongoDBAcctName}-${uniqueString(resourceGroup().id)}'
//   params: {
//     parLocation: parLocation
//     parLocAbbrev: parLocAbbrev
//     parCompanyPrefix: parCompanyPrefix
//     parEnvironment: parEnvironment
//     parMongoDBAcctName: mongoDb.MongoDBAcctName
//     parMongoSubId: mongoDb.MongoSubId
//     parMongoResourceGroupName: mongoDb.MongoResourceGroupName
//     parSpokeName: parSpokeName
//     parSpokeSubId: parSpokeSubId
//     parHubSubId: parHubSubId
//     parHubNetworkResourceGroupName: parHubNetworkResourceGroupName
//   }
//   dependsOn: [
//     apps
//   ]
// }]
